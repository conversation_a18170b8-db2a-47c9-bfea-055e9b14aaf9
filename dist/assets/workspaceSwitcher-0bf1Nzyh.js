const STORAGE_KEYS = {
  WORKSPACES: "workspaces",
  SETTINGS: "settings",
  ACTIVE_WORKSPACE_ID: "activeWorkspaceId",
  LAST_ACTIVE_WORKSPACE_IDS: "lastActiveWorkspaceIds",
  TAB_SESSIONS: "tabSessions",
  TAB_STATE_SNAPSHOTS: "tabStateSnapshots",
  WORKSPACE_TAB_MAPPINGS: "workspaceTabMappings"
};
const DEFAULT_SETTINGS = {
  autoCloseOtherTabs: true,
  preserveUserOpenedTabs: false,
  defaultWorkspaceOnStartup: "",
  sidebarWidth: 320,
  theme: "dark",
  showFavicons: true,
  confirmBeforeDelete: true,
  maxRecentWorkspaces: 5
};
const WORKSPACE_COLORS = [
  "#3b82f6",
  // blue
  "#10b981",
  // emerald
  "#f59e0b",
  // amber
  "#ef4444",
  // red
  "#8b5cf6",
  // violet
  "#06b6d4",
  // cyan
  "#84cc16",
  // lime
  "#f97316",
  // orange
  "#ec4899",
  // pink
  "#6366f1"
  // indigo
];
const WORKSPACE_ICONS = [
  "🚀",
  "💼",
  "🔬",
  "🎨",
  "📊",
  "🛠️",
  "📚",
  "💡",
  "🎯",
  "⚡",
  "🌟",
  "🔥",
  "💎",
  "🎪",
  "🎭",
  "🎨",
  "🎵",
  "🎮",
  "🏆",
  "🎊",
  "📱",
  "💻",
  "🖥️",
  "⌨️",
  "🖱️",
  "🖨️",
  "📷",
  "📹",
  "🎥",
  "📺",
  "🔍",
  "🔎",
  "🔬",
  "🔭",
  "📡",
  "🛰️",
  "🚁",
  "✈️",
  "🚀",
  "🛸"
];
const WORKSPACE_TEMPLATES = [
  {
    id: "ai-tools",
    name: "AI工具集",
    description: "常用的AI工具和平台",
    icon: "🤖",
    color: "#3b82f6",
    category: "ai-tools",
    websites: [
      {
        url: "https://chat.openai.com",
        title: "ChatGPT",
        favicon: "https://chat.openai.com/favicon.ico",
        isPinned: true
      },
      {
        url: "https://claude.ai",
        title: "Claude",
        favicon: "https://claude.ai/favicon.ico",
        isPinned: true
      },
      {
        url: "https://gemini.google.com",
        title: "Gemini",
        favicon: "https://gemini.google.com/favicon.ico",
        isPinned: true
      }
    ]
  },
  {
    id: "development",
    name: "开发环境",
    description: "编程开发相关工具",
    icon: "💻",
    color: "#10b981",
    category: "development",
    websites: [
      {
        url: "https://github.com",
        title: "GitHub",
        favicon: "https://github.com/favicon.ico",
        isPinned: true
      },
      {
        url: "https://stackoverflow.com",
        title: "Stack Overflow",
        favicon: "https://stackoverflow.com/favicon.ico",
        isPinned: true
      },
      {
        url: "https://developer.mozilla.org",
        title: "MDN Web Docs",
        favicon: "https://developer.mozilla.org/favicon.ico",
        isPinned: true
      }
    ]
  },
  {
    id: "design",
    name: "设计工具",
    description: "设计和创意工具",
    icon: "🎨",
    color: "#f59e0b",
    category: "design",
    websites: [
      {
        url: "https://www.figma.com",
        title: "Figma",
        favicon: "https://www.figma.com/favicon.ico",
        isPinned: true
      },
      {
        url: "https://dribbble.com",
        title: "Dribbble",
        favicon: "https://dribbble.com/favicon.ico",
        isPinned: true
      },
      {
        url: "https://www.behance.net",
        title: "Behance",
        favicon: "https://www.behance.net/favicon.ico",
        isPinned: true
      }
    ]
  },
  {
    id: "research",
    name: "学术研究",
    description: "学术研究和论文查找",
    icon: "🔬",
    color: "#8b5cf6",
    category: "research",
    websites: [
      {
        url: "https://arxiv.org",
        title: "arXiv",
        favicon: "https://arxiv.org/favicon.ico",
        isPinned: true
      },
      {
        url: "https://scholar.google.com",
        title: "Google Scholar",
        favicon: "https://scholar.google.com/favicon.ico",
        isPinned: true
      },
      {
        url: "https://www.researchgate.net",
        title: "ResearchGate",
        favicon: "https://www.researchgate.net/favicon.ico",
        isPinned: true
      }
    ]
  },
  {
    id: "productivity",
    name: "生产力工具",
    description: "提高工作效率的工具",
    icon: "⚡",
    color: "#06b6d4",
    category: "productivity",
    websites: [
      {
        url: "https://notion.so",
        title: "Notion",
        favicon: "https://notion.so/favicon.ico",
        isPinned: true
      },
      {
        url: "https://trello.com",
        title: "Trello",
        favicon: "https://trello.com/favicon.ico",
        isPinned: true
      },
      {
        url: "https://calendar.google.com",
        title: "Google Calendar",
        favicon: "https://calendar.google.com/favicon.ico",
        isPinned: true
      }
    ]
  }
];
const ERROR_CODES = {
  WORKSPACE_NOT_FOUND: "WORKSPACE_NOT_FOUND",
  WEBSITE_NOT_FOUND: "WEBSITE_NOT_FOUND",
  STORAGE_ERROR: "STORAGE_ERROR",
  TAB_ERROR: "TAB_ERROR",
  WINDOW_ERROR: "WINDOW_ERROR",
  PERMISSION_DENIED: "PERMISSION_DENIED",
  INVALID_URL: "INVALID_URL",
  DUPLICATE_WORKSPACE: "DUPLICATE_WORKSPACE",
  DUPLICATE_WEBSITE: "DUPLICATE_WEBSITE"
};
const COMMANDS = {
  SWITCH_WORKSPACE_1: "switch-workspace-1",
  SWITCH_WORKSPACE_2: "switch-workspace-2",
  SWITCH_WORKSPACE_3: "switch-workspace-3",
  TOGGLE_SIDEPANEL: "toggle-sidepanel"
};
const URL_REGEX = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;
const DEFAULT_FAVICON = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="m9 12 2 2 4-4"/></svg>';

class StorageManager {
  /**
   * 获取所有存储数据
   */
  static async getAllData() {
    try {
      const result = await chrome.storage.local.get([
        STORAGE_KEYS.WORKSPACES,
        STORAGE_KEYS.SETTINGS,
        STORAGE_KEYS.ACTIVE_WORKSPACE_ID,
        STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS
      ]);
      const data = {
        workspaces: result[STORAGE_KEYS.WORKSPACES] || [],
        settings: { ...DEFAULT_SETTINGS, ...result[STORAGE_KEYS.SETTINGS] },
        activeWorkspaceId: result[STORAGE_KEYS.ACTIVE_WORKSPACE_ID] || null,
        lastActiveWorkspaceIds: result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || []
      };
      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get storage data",
          details: error
        }
      };
    }
  }
  /**
   * 获取所有工作区
   */
  static async getWorkspaces() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.WORKSPACES);
      const workspaces = result[STORAGE_KEYS.WORKSPACES] || [];
      workspaces.sort((a, b) => a.order - b.order);
      return { success: true, data: workspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 保存工作区列表
   */
  static async saveWorkspaces(workspaces) {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.WORKSPACES]: workspaces
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 获取单个工作区
   */
  static async getWorkspace(id) {
    const result = await this.getWorkspaces();
    if (!result.success) {
      return {
        success: false,
        error: result.error
      };
    }
    const workspace = result.data.find((w) => w.id === id);
    if (!workspace) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_NOT_FOUND,
          message: `Workspace with id ${id} not found`
        }
      };
    }
    return { success: true, data: workspace };
  }
  /**
   * 获取设置
   */
  static async getSettings() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.SETTINGS);
      const settings = { ...DEFAULT_SETTINGS, ...result[STORAGE_KEYS.SETTINGS] };
      return { success: true, data: settings };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get settings",
          details: error
        }
      };
    }
  }
  /**
   * 保存设置
   */
  static async saveSettings(settings) {
    try {
      const currentResult = await this.getSettings();
      if (!currentResult.success) {
        return {
          success: false,
          error: currentResult.error
        };
      }
      const updatedSettings = { ...currentResult.data, ...settings };
      await chrome.storage.local.set({
        [STORAGE_KEYS.SETTINGS]: updatedSettings
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save settings",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前活跃工作区ID
   */
  static async getActiveWorkspaceId() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.ACTIVE_WORKSPACE_ID);
      return { success: true, data: result[STORAGE_KEYS.ACTIVE_WORKSPACE_ID] || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get active workspace ID",
          details: error
        }
      };
    }
  }
  /**
   * 设置当前活跃工作区ID
   */
  static async setActiveWorkspaceId(id) {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.ACTIVE_WORKSPACE_ID]: id
      });
      if (id) {
        await this.updateLastActiveWorkspaces(id);
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to set active workspace ID",
          details: error
        }
      };
    }
  }
  /**
   * 更新最近使用的工作区列表
   */
  static async updateLastActiveWorkspaces(workspaceId) {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS);
      let lastActiveIds = result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || [];
      lastActiveIds = lastActiveIds.filter((id) => id !== workspaceId);
      lastActiveIds.unshift(workspaceId);
      const settingsResult = await this.getSettings();
      const maxRecent = settingsResult.success ? settingsResult.data.maxRecentWorkspaces : 5;
      lastActiveIds = lastActiveIds.slice(0, maxRecent);
      await chrome.storage.local.set({
        [STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS]: lastActiveIds
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update last active workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 清除所有数据
   */
  static async clearAll() {
    try {
      await chrome.storage.local.clear();
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to clear storage",
          details: error
        }
      };
    }
  }
  /**
   * 监听存储变化
   */
  static onChanged(callback) {
    chrome.storage.onChanged.addListener((changes, areaName) => {
      if (areaName === "local") {
        callback(changes);
      }
    });
  }
  /**
   * 导出数据
   */
  static async exportData() {
    try {
      const dataResult = await this.getAllData();
      if (!dataResult.success) {
        return {
          success: false,
          error: dataResult.error
        };
      }
      const exportData = {
        version: "1.0.0",
        exportedAt: Date.now(),
        workspaces: dataResult.data.workspaces,
        settings: dataResult.data.settings
      };
      return { success: true, data: JSON.stringify(exportData, null, 2) };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to export data",
          details: error
        }
      };
    }
  }
  /**
   * 导入数据
   */
  static async importData(jsonData) {
    try {
      const importData = JSON.parse(jsonData);
      if (importData.workspaces) {
        await this.saveWorkspaces(importData.workspaces);
      }
      if (importData.settings) {
        await this.saveSettings(importData.settings);
      }
      if (importData.tabSessions) {
        await this.saveTabSessions(importData.tabSessions);
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to import data",
          details: error
        }
      };
    }
  }
  /**
   * 获取标签页会话列表
   */
  static async getTabSessions() {
    try {
      const result = await chrome.storage.local.get("tabSessions");
      const sessions = result.tabSessions || [];
      return { success: true, data: sessions };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get tab sessions",
          details: error
        }
      };
    }
  }
  /**
   * 保存标签页会话列表
   */
  static async saveTabSessions(sessions) {
    try {
      await chrome.storage.local.set({ tabSessions: sessions });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save tab sessions",
          details: error
        }
      };
    }
  }
  /**
   * 获取标签页状态快照列表
   */
  static async getTabStateSnapshots() {
    try {
      const result = await chrome.storage.local.get("tabStateSnapshots");
      const snapshots = result.tabStateSnapshots || [];
      return { success: true, data: snapshots };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get tab state snapshots",
          details: error
        }
      };
    }
  }
  /**
   * 保存标签页状态快照列表
   */
  static async saveTabStateSnapshots(snapshots) {
    try {
      await chrome.storage.local.set({ tabStateSnapshots: snapshots });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save tab state snapshots",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区标签页映射列表
   */
  static async getWorkspaceTabMappings() {
    try {
      const result = await chrome.storage.local.get("workspaceTabMappings");
      const mappings = result.workspaceTabMappings || [];
      return { success: true, data: mappings };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get workspace tab mappings",
          details: error
        }
      };
    }
  }
  /**
   * 保存工作区标签页映射列表
   */
  static async saveWorkspaceTabMappings(mappings) {
    try {
      await chrome.storage.local.set({ workspaceTabMappings: mappings });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save workspace tab mappings",
          details: error
        }
      };
    }
  }
  /**
   * 获取最近活跃的工作区ID列表
   */
  static async getLastActiveWorkspaceIds() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS);
      const ids = result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || [];
      return { success: true, data: ids };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get last active workspace IDs",
          details: error
        }
      };
    }
  }
  /**
   * 设置最近活跃的工作区ID列表
   */
  static async setLastActiveWorkspaceIds(ids) {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS]: ids
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to set last active workspace IDs",
          details: error
        }
      };
    }
  }
  /**
   * 清理所有存储数据（用于重置）
   */
  static async clearAllData() {
    try {
      await chrome.storage.local.clear();
      console.log("所有存储数据已清理");
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to clear all data",
          details: error
        }
      };
    }
  }
  /**
   * 获取存储使用情况
   */
  static async getStorageUsage() {
    try {
      const usage = await chrome.storage.local.getBytesInUse();
      const quota = chrome.storage.local.QUOTA_BYTES;
      return {
        success: true,
        data: {
          used: usage,
          quota
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get storage usage",
          details: error
        }
      };
    }
  }
}

class WindowManager {
  static workspaceWindows = /* @__PURE__ */ new Map();
  // workspaceId -> windowId
  static windowWorkspaces = /* @__PURE__ */ new Map();
  // windowId -> workspaceId
  static hiddenWindows = /* @__PURE__ */ new Set();
  // 隐藏的窗口ID集合
  static STORAGE_WINDOW_BOUNDS = {
    width: 400,
    height: 300,
    left: -1e3,
    // 移到屏幕外
    top: -1e3
  };
  /**
   * 为工作区创建专用窗口
   */
  static async createWorkspaceWindow(workspaceId, workspaceName) {
    try {
      console.log(`创建工作区专用窗口: ${workspaceName} (${workspaceId})`);
      const existingWindowId = this.workspaceWindows.get(workspaceId);
      if (existingWindowId) {
        try {
          const window2 = await chrome.windows.get(existingWindowId);
          if (window2) {
            console.log(`工作区 ${workspaceName} 的专用窗口已存在: ${existingWindowId}`);
            return {
              success: true,
              data: {
                id: existingWindowId,
                workspaceId,
                workspaceName,
                tabCount: window2.tabs?.length || 0,
                isVisible: window2.state !== "minimized"
              }
            };
          }
        } catch {
          this.workspaceWindows.delete(workspaceId);
          this.windowWorkspaces.delete(existingWindowId);
        }
      }
      const window = await chrome.windows.create({
        type: "normal",
        state: "normal",
        focused: false,
        // 不获取焦点
        width: this.STORAGE_WINDOW_BOUNDS.width,
        height: this.STORAGE_WINDOW_BOUNDS.height,
        left: this.STORAGE_WINDOW_BOUNDS.left,
        top: this.STORAGE_WINDOW_BOUNDS.top,
        url: chrome.runtime.getURL("workspace-placeholder.html") + `?workspaceId=${workspaceId}&workspaceName=${encodeURIComponent(workspaceName)}`
      });
      if (window.id) {
        this.hiddenWindows.add(window.id);
        try {
          await this.hideWorkspaceWindow(window.id);
          console.log(`成功隐藏工作区专用窗口: ${workspaceName} -> 窗口ID ${window.id}`);
        } catch (error) {
          console.warn(`隐藏窗口失败，但窗口创建成功: ${workspaceName}`, error);
        }
      }
      if (!window.id) {
        throw new Error("Failed to create window");
      }
      this.workspaceWindows.set(workspaceId, window.id);
      this.windowWorkspaces.set(window.id, workspaceId);
      console.log(`成功创建工作区专用窗口: ${workspaceName} -> 窗口ID ${window.id}`);
      return {
        success: true,
        data: {
          id: window.id,
          workspaceId,
          workspaceName,
          tabCount: window.tabs?.length || 1,
          isVisible: false
          // 窗口默认最小化，所以不可见
        }
      };
    } catch (error) {
      console.error(`创建工作区专用窗口失败: ${workspaceName}`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to create workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区的专用窗口ID
   */
  static getWorkspaceWindowId(workspaceId) {
    return this.workspaceWindows.get(workspaceId);
  }
  /**
   * 获取窗口对应的工作区ID
   */
  static getWindowWorkspaceId(windowId) {
    return this.windowWorkspaces.get(windowId);
  }
  /**
   * 将标签页移动到工作区专用窗口
   */
  static async moveTabsToWorkspaceWindow(tabIds, workspaceId, workspaceName) {
    try {
      if (tabIds.length === 0) {
        return { success: true };
      }
      console.log(`移动 ${tabIds.length} 个标签页到工作区 ${workspaceName}`);
      const windowResult = await this.createWorkspaceWindow(workspaceId, workspaceName);
      if (!windowResult.success) {
        return { success: false, error: windowResult.error };
      }
      const windowId = windowResult.data.id;
      await chrome.tabs.move(tabIds, {
        windowId,
        index: -1
        // 移动到窗口末尾
      });
      await this.hideWorkspaceWindow(windowId);
      console.log(`开始自动挂起移动到工作区专用窗口的 ${tabIds.length} 个标签页`);
      let suspendedCount = 0;
      let failedCount = 0;
      for (const tabId of tabIds) {
        try {
          const tab = await chrome.tabs.get(tabId);
          if (tab && !tab.active && !tab.discarded) {
            await chrome.tabs.discard(tabId);
            suspendedCount++;
            console.log(`成功挂起标签页: ${tab.title} (ID: ${tabId})`);
          } else if (tab && tab.discarded) {
            console.log(`标签页已经是挂起状态: ${tab.title} (ID: ${tabId})`);
          } else if (tab && tab.active) {
            console.log(`跳过活跃标签页: ${tab.title} (ID: ${tabId})`);
          }
        } catch (error) {
          failedCount++;
          console.warn(`挂起标签页 ${tabId} 失败:`, error);
        }
      }
      console.log(`标签页挂起操作完成: 成功 ${suspendedCount} 个，失败 ${failedCount} 个`);
      console.log(`成功移动 ${tabIds.length} 个标签页到工作区专用窗口 ${windowId}`);
      return { success: true };
    } catch (error) {
      console.error(`移动标签页到工作区专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move tabs to workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 将标签页从工作区专用窗口移动到主窗口
   */
  static async moveTabsFromWorkspaceWindow(workspaceId, targetWindowId) {
    try {
      const windowId = this.workspaceWindows.get(workspaceId);
      if (!windowId) {
        console.log(`工作区 ${workspaceId} 没有专用窗口`);
        return { success: true, data: [] };
      }
      const tabs = await chrome.tabs.query({ windowId });
      const workspaceTabs = tabs.filter(
        (tab) => !tab.url?.includes("workspace-placeholder.html")
      );
      if (workspaceTabs.length === 0) {
        console.log(`工作区专用窗口 ${windowId} 中没有需要移动的标签页`);
        return { success: true, data: [] };
      }
      let targetWindow = targetWindowId;
      if (!targetWindow) {
        const currentWindow = await chrome.windows.getCurrent();
        targetWindow = currentWindow.id;
      }
      console.log(`从工作区专用窗口 ${windowId} 移动 ${workspaceTabs.length} 个标签页到窗口 ${targetWindow}`);
      const tabIds = workspaceTabs.map((tab) => tab.id);
      await chrome.tabs.move(tabIds, {
        windowId: targetWindow,
        index: -1
      });
      const tabInfos = workspaceTabs.map((tab) => ({
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: targetWindow
      }));
      console.log(`成功移动 ${workspaceTabs.length} 个标签页到主窗口`);
      return { success: true, data: tabInfos };
    } catch (error) {
      console.error(`从工作区专用窗口移动标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move tabs from workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 关闭工作区专用窗口
   */
  static async closeWorkspaceWindow(workspaceId) {
    try {
      const windowId = this.workspaceWindows.get(workspaceId);
      if (!windowId) {
        console.log(`工作区 ${workspaceId} 没有专用窗口需要关闭`);
        return { success: true };
      }
      console.log(`关闭工作区专用窗口: ${windowId}`);
      await this.moveTabsFromWorkspaceWindow(workspaceId);
      await chrome.windows.remove(windowId);
      this.workspaceWindows.delete(workspaceId);
      this.windowWorkspaces.delete(windowId);
      console.log(`成功关闭工作区专用窗口: ${windowId}`);
      return { success: true };
    } catch (error) {
      console.error(`关闭工作区专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to close workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 获取所有工作区专用窗口信息
   */
  static async getAllWorkspaceWindows() {
    try {
      const windowInfos = [];
      for (const [workspaceId, windowId] of this.workspaceWindows.entries()) {
        try {
          const window = await chrome.windows.get(windowId, { populate: true });
          const workspaceName = this.windowWorkspaces.get(windowId) || "Unknown";
          windowInfos.push({
            id: windowId,
            workspaceId,
            workspaceName,
            tabCount: window.tabs?.length || 0,
            isVisible: window.state !== "minimized"
          });
        } catch {
          this.workspaceWindows.delete(workspaceId);
          this.windowWorkspaces.delete(windowId);
        }
      }
      return { success: true, data: windowInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to get workspace windows",
          details: error
        }
      };
    }
  }
  /**
   * 更新窗口标题
   */
  static async updateWindowTitle(workspaceId, workspaceName, tabCount) {
    try {
      const windowId = this.workspaceWindows.get(workspaceId);
      if (!windowId) {
        return { success: true };
      }
      const tabs = await chrome.tabs.query({ windowId });
      const placeholderTab = tabs.find(
        (tab) => tab.url?.includes("workspace-placeholder.html")
      );
      if (placeholderTab) {
        const newUrl = chrome.runtime.getURL("workspace-placeholder.html") + `?workspaceId=${workspaceId}&workspaceName=${encodeURIComponent(workspaceName)}&tabCount=${tabCount}`;
        await chrome.tabs.update(placeholderTab.id, { url: newUrl });
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to update window title",
          details: error
        }
      };
    }
  }
  /**
   * 隐藏工作区专用窗口
   */
  static async hideWorkspaceWindow(windowId) {
    try {
      await chrome.windows.update(windowId, {
        left: this.STORAGE_WINDOW_BOUNDS.left,
        top: this.STORAGE_WINDOW_BOUNDS.top,
        width: this.STORAGE_WINDOW_BOUNDS.width,
        height: this.STORAGE_WINDOW_BOUNDS.height,
        state: "minimized",
        focused: false
      });
      this.hiddenWindows.add(windowId);
      console.log(`窗口 ${windowId} 已隐藏`);
      return { success: true };
    } catch (error) {
      console.error(`隐藏窗口 ${windowId} 失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to hide workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 显示工作区专用窗口
   */
  static async showWorkspaceWindow(windowId) {
    try {
      await chrome.windows.update(windowId, {
        left: 100,
        top: 100,
        width: 1200,
        height: 800,
        state: "normal",
        focused: true
      });
      this.hiddenWindows.delete(windowId);
      console.log(`窗口 ${windowId} 已显示`);
      return { success: true };
    } catch (error) {
      console.error(`显示窗口 ${windowId} 失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to show workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 检查窗口是否被隐藏
   */
  static isWindowHidden(windowId) {
    return this.hiddenWindows.has(windowId);
  }
  /**
   * 获取所有隐藏的窗口ID
   */
  static getHiddenWindows() {
    return Array.from(this.hiddenWindows);
  }
  /**
   * 清理无效的窗口映射
   */
  static async cleanupInvalidWindows() {
    try {
      const invalidWorkspaceIds = [];
      const invalidWindowIds = [];
      for (const [workspaceId, windowId] of this.workspaceWindows.entries()) {
        try {
          await chrome.windows.get(windowId);
        } catch {
          invalidWorkspaceIds.push(workspaceId);
          invalidWindowIds.push(windowId);
        }
      }
      for (const workspaceId of invalidWorkspaceIds) {
        this.workspaceWindows.delete(workspaceId);
      }
      for (const windowId of invalidWindowIds) {
        this.windowWorkspaces.delete(windowId);
        this.hiddenWindows.delete(windowId);
      }
      if (invalidWorkspaceIds.length > 0) {
        console.log(`清理了 ${invalidWorkspaceIds.length} 个无效的窗口映射`);
      }
      return { success: true };
    } catch (error) {
      console.error("清理无效窗口映射失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to cleanup invalid windows",
          details: error
        }
      };
    }
  }
  /**
   * 确保工作区窗口处于正确的隐藏状态
   */
  static async ensureWindowsHidden() {
    try {
      for (const [workspaceId, windowId] of this.workspaceWindows.entries()) {
        try {
          const window = await chrome.windows.get(windowId);
          if (window.state !== "minimized" || window.left !== this.STORAGE_WINDOW_BOUNDS.left) {
            await this.hideWorkspaceWindow(windowId);
            console.log(`重新隐藏工作区窗口: ${workspaceId}`);
          }
        } catch {
          this.workspaceWindows.delete(workspaceId);
          this.windowWorkspaces.delete(windowId);
          this.hiddenWindows.delete(windowId);
        }
      }
      return { success: true };
    } catch (error) {
      console.error("确保窗口隐藏状态失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to ensure windows hidden",
          details: error
        }
      };
    }
  }
}

class WorkspaceManager {
  /**
   * 生成唯一ID
   */
  static generateId() {
    return `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  /**
   * 生成网站ID
   */
  static generateWebsiteId() {
    return `site_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  /**
   * 验证URL格式
   */
  static isValidUrl(url) {
    return URL_REGEX.test(url);
  }
  /**
   * 获取网站favicon
   */
  static async getFavicon(url) {
    try {
      const domain = new URL(url).origin;
      return `${domain}/favicon.ico`;
    } catch {
      return DEFAULT_FAVICON;
    }
  }
  /**
   * 获取网站标题
   */
  static async getWebsiteTitle(url) {
    try {
      const tabs = await chrome.tabs.query({ url });
      if (tabs.length > 0 && tabs[0].title) {
        return tabs[0].title;
      }
      const domain = new URL(url).hostname;
      return domain.replace("www.", "");
    } catch {
      return url;
    }
  }
  /**
   * 创建新工作区
   */
  static async createWorkspace(options) {
    try {
      if (!options.name.trim()) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_URL,
            message: "Workspace name cannot be empty"
          }
        };
      }
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error
        };
      }
      const existingWorkspaces = workspacesResult.data;
      if (existingWorkspaces.some((w) => w.name === options.name)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.DUPLICATE_WORKSPACE,
            message: "Workspace with this name already exists"
          }
        };
      }
      const workspace = {
        id: this.generateId(),
        name: options.name,
        icon: options.icon || WORKSPACE_ICONS[Math.floor(Math.random() * WORKSPACE_ICONS.length)],
        color: options.color || WORKSPACE_COLORS[Math.floor(Math.random() * WORKSPACE_COLORS.length)],
        websites: [],
        createdAt: Date.now(),
        updatedAt: Date.now(),
        isActive: false,
        order: existingWorkspaces.length
      };
      if (options.websites) {
        for (let i = 0; i < options.websites.length; i++) {
          const siteData = options.websites[i];
          const website = {
            id: this.generateWebsiteId(),
            url: siteData.url,
            title: siteData.title || await this.getWebsiteTitle(siteData.url),
            favicon: siteData.favicon || await this.getFavicon(siteData.url),
            isPinned: siteData.isPinned,
            addedAt: Date.now(),
            order: i
          };
          workspace.websites.push(website);
        }
      }
      existingWorkspaces.push(workspace);
      const saveResult = await StorageManager.saveWorkspaces(existingWorkspaces);
      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.error
        };
      }
      if (options.activate) {
        await StorageManager.setActiveWorkspaceId(workspace.id);
        workspace.isActive = true;
      }
      return { success: true, data: workspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to create workspace",
          details: error
        }
      };
    }
  }
  /**
   * 更新工作区
   */
  static async updateWorkspace(id, options) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspaceIndex = workspaces.findIndex((w) => w.id === id);
      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${id} not found`
          }
        };
      }
      const workspace = workspaces[workspaceIndex];
      if (options.name !== void 0) workspace.name = options.name;
      if (options.icon !== void 0) workspace.icon = options.icon;
      if (options.color !== void 0) workspace.color = options.color;
      if (options.websites !== void 0) workspace.websites = options.websites;
      if (options.isActive !== void 0) workspace.isActive = options.isActive;
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      return { success: true, data: workspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update workspace",
          details: error
        }
      };
    }
  }
  /**
   * 删除工作区
   */
  static async deleteWorkspace(id) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspaceIndex = workspaces.findIndex((w) => w.id === id);
      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${id} not found`
          }
        };
      }
      console.log(`删除工作区 ${id}，关闭专用窗口`);
      const closeWindowResult = await WindowManager.closeWorkspaceWindow(id);
      if (!closeWindowResult.success) {
        console.error(`关闭工作区专用窗口失败:`, closeWindowResult.error);
      }
      workspaces.splice(workspaceIndex, 1);
      workspaces.forEach((workspace, index) => {
        workspace.order = index;
      });
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (activeIdResult.success && activeIdResult.data === id) {
        await StorageManager.setActiveWorkspaceId(null);
      }
      console.log(`成功删除工作区: ${id}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to delete workspace",
          details: error
        }
      };
    }
  }
  /**
   * 添加网站到工作区
   */
  static async addWebsite(workspaceId, url, options = {}) {
    try {
      if (!this.isValidUrl(url)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_URL,
            message: "Invalid URL format"
          }
        };
      }
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`
          }
        };
      }
      if (workspace.websites.some((w) => w.url === url)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.DUPLICATE_WEBSITE,
            message: "Website with this URL already exists in workspace"
          }
        };
      }
      const website = {
        id: this.generateWebsiteId(),
        url,
        title: options.title || await this.getWebsiteTitle(url),
        favicon: options.favicon || await this.getFavicon(url),
        isPinned: options.pinTab || false,
        addedAt: Date.now(),
        order: workspace.websites.length
      };
      workspace.websites.push(website);
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      if (options.openInNewTab) {
        await chrome.tabs.create({
          url,
          pinned: options.pinTab || false
        });
      }
      return { success: true, data: website };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to add website",
          details: error
        }
      };
    }
  }
  /**
   * 从工作区移除网站
   */
  static async removeWebsite(workspaceId, websiteId) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`
          }
        };
      }
      const websiteIndex = workspace.websites.findIndex((w) => w.id === websiteId);
      if (websiteIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WEBSITE_NOT_FOUND,
            message: `Website with id ${websiteId} not found`
          }
        };
      }
      workspace.websites.splice(websiteIndex, 1);
      workspace.websites.forEach((website, index) => {
        website.order = index;
      });
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to remove website",
          details: error
        }
      };
    }
  }
  /**
   * 更新工作区中的网站
   */
  static async updateWebsite(workspaceId, websiteId, updates) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error
        };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`
          }
        };
      }
      const website = workspace.websites.find((w) => w.id === websiteId);
      if (!website) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WEBSITE_NOT_FOUND,
            message: `Website with id ${websiteId} not found`
          }
        };
      }
      if (updates.url !== void 0) {
        if (!this.isValidUrl(updates.url)) {
          return {
            success: false,
            error: {
              code: ERROR_CODES.INVALID_URL,
              message: "Invalid URL format"
            }
          };
        }
        website.url = updates.url;
        website.favicon = await this.getFavicon(updates.url);
      }
      if (updates.title !== void 0) {
        website.title = updates.title;
      }
      if (updates.isPinned !== void 0) {
        website.isPinned = updates.isPinned;
      }
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.error
        };
      }
      return { success: true, data: website };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update website",
          details: error
        }
      };
    }
  }
  /**
   * 重新排序工作区
   */
  static async reorderWorkspaces(workspaceIds) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const reorderedWorkspaces = [];
      workspaceIds.forEach((id, index) => {
        const workspace = workspaces.find((w) => w.id === id);
        if (workspace) {
          workspace.order = index;
          reorderedWorkspaces.push(workspace);
        }
      });
      const saveResult = await StorageManager.saveWorkspaces(reorderedWorkspaces);
      if (!saveResult.success) return saveResult;
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to reorder workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 重新排序工作区内的网站
   */
  static async reorderWebsites(workspaceId, websiteIds) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`
          }
        };
      }
      const reorderedWebsites = [];
      websiteIds.forEach((id, index) => {
        const website = workspace.websites.find((w) => w.id === id);
        if (website) {
          website.order = index;
          reorderedWebsites.push(website);
        }
      });
      workspace.websites = reorderedWebsites;
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to reorder websites",
          details: error
        }
      };
    }
  }
}

class PerformanceManager {
  static metrics = [];
  static MAX_METRICS_HISTORY = 100;
  static PERFORMANCE_THRESHOLD_MS = 2e3;
  // 2秒性能阈值
  // 默认批处理配置
  static DEFAULT_BATCH_CONFIG = {
    batchSize: 10,
    delayBetweenBatches: 100,
    maxConcurrent: 3,
    timeoutPerBatch: 5e3
  };
  /**
   * 开始性能监控
   */
  static startPerformanceMonitoring(operationName) {
    const monitoringId = `${operationName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = performance.now();
    globalThis[`perf_${monitoringId}`] = {
      operationName,
      startTime,
      tabCount: 0
    };
    console.log(`开始性能监控: ${operationName} (ID: ${monitoringId})`);
    return monitoringId;
  }
  /**
   * 结束性能监控
   */
  static endPerformanceMonitoring(monitoringId, success = true, tabCount, errorMessage) {
    try {
      const perfData = globalThis[`perf_${monitoringId}`];
      if (!perfData) {
        console.warn(`性能监控数据未找到: ${monitoringId}`);
        return null;
      }
      const endTime = performance.now();
      const duration = endTime - perfData.startTime;
      const metrics = {
        operationName: perfData.operationName,
        startTime: perfData.startTime,
        endTime,
        duration,
        success,
        tabCount: tabCount || perfData.tabCount,
        errorMessage
      };
      this.recordMetrics(metrics);
      if (duration > this.PERFORMANCE_THRESHOLD_MS) {
        console.warn(`性能警告: ${perfData.operationName} 耗时 ${duration.toFixed(2)}ms，超过阈值 ${this.PERFORMANCE_THRESHOLD_MS}ms`);
      }
      delete globalThis[`perf_${monitoringId}`];
      console.log(`性能监控结束: ${perfData.operationName}, 耗时: ${duration.toFixed(2)}ms, 成功: ${success}`);
      return metrics;
    } catch (error) {
      console.error("结束性能监控失败:", error);
      return null;
    }
  }
  /**
   * 记录性能指标
   */
  static recordMetrics(metrics) {
    this.metrics.push(metrics);
    if (this.metrics.length > this.MAX_METRICS_HISTORY) {
      this.metrics.shift();
    }
  }
  /**
   * 获取性能统计
   */
  static getPerformanceStats() {
    if (this.metrics.length === 0) {
      return {
        totalOperations: 0,
        averageDuration: 0,
        successRate: 0,
        slowOperations: []
      };
    }
    const totalDuration = this.metrics.reduce((sum, m) => sum + m.duration, 0);
    const successCount = this.metrics.filter((m) => m.success).length;
    const slowOperations = this.metrics.filter((m) => m.duration > this.PERFORMANCE_THRESHOLD_MS);
    return {
      totalOperations: this.metrics.length,
      averageDuration: totalDuration / this.metrics.length,
      successRate: successCount / this.metrics.length * 100,
      slowOperations
    };
  }
  /**
   * 批处理标签页操作
   */
  static async batchProcessTabs(items, processor, config = {}) {
    const finalConfig = { ...this.DEFAULT_BATCH_CONFIG, ...config };
    const monitoringId = this.startPerformanceMonitoring(`batch_process_${items.length}_items`);
    try {
      console.log(`开始批处理 ${items.length} 个项目，批大小: ${finalConfig.batchSize}`);
      const results = [];
      const errors = [];
      for (let i = 0; i < items.length; i += finalConfig.batchSize) {
        const batch = items.slice(i, i + finalConfig.batchSize);
        console.log(`处理批次 ${Math.floor(i / finalConfig.batchSize) + 1}/${Math.ceil(items.length / finalConfig.batchSize)}`);
        try {
          const batchPromises = batch.map(
            (item) => this.withTimeout(processor(item), finalConfig.timeoutPerBatch)
          );
          const batchResults = await Promise.allSettled(batchPromises);
          batchResults.forEach((result, index) => {
            if (result.status === "fulfilled") {
              results.push(result.value);
            } else {
              errors.push({
                item: batch[index],
                error: result.reason
              });
              console.error(`批处理项目失败:`, result.reason);
            }
          });
          if (i + finalConfig.batchSize < items.length) {
            await this.delay(finalConfig.delayBetweenBatches);
          }
        } catch (error) {
          console.error(`批次处理失败:`, error);
          errors.push({ batch, error });
        }
      }
      this.endPerformanceMonitoring(monitoringId, errors.length === 0, items.length);
      if (errors.length > 0) {
        console.warn(`批处理完成，但有 ${errors.length} 个错误`);
      }
      return {
        success: true,
        data: results
      };
    } catch (error) {
      this.endPerformanceMonitoring(monitoringId, false, items.length, error.message);
      console.error("批处理失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Batch processing failed",
          details: error
        }
      };
    }
  }
  /**
   * 带超时的Promise包装
   */
  static withTimeout(promise, timeoutMs) {
    return Promise.race([
      promise,
      new Promise(
        (_, reject) => setTimeout(() => reject(new Error(`Operation timeout after ${timeoutMs}ms`)), timeoutMs)
      )
    ]);
  }
  /**
   * 延迟函数
   */
  static delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
  /**
   * 重试机制
   */
  static async retryOperation(operation, maxRetries = 3, delayMs = 1e3, backoffMultiplier = 2) {
    let lastError;
    let currentDelay = delayMs;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`尝试操作，第 ${attempt}/${maxRetries} 次`);
        const result = await operation();
        if (attempt > 1) {
          console.log(`操作在第 ${attempt} 次尝试后成功`);
        }
        return { success: true, data: result };
      } catch (error) {
        lastError = error;
        console.warn(`操作失败，第 ${attempt}/${maxRetries} 次尝试:`, error);
        if (attempt < maxRetries) {
          console.log(`等待 ${currentDelay}ms 后重试...`);
          await this.delay(currentDelay);
          currentDelay *= backoffMultiplier;
        }
      }
    }
    console.error(`操作在 ${maxRetries} 次尝试后仍然失败:`, lastError);
    return {
      success: false,
      error: {
        code: ERROR_CODES.TAB_ERROR,
        message: `Operation failed after ${maxRetries} retries`,
        details: lastError
      }
    };
  }
  /**
   * 内存使用监控
   */
  static getMemoryUsage() {
    if ("memory" in performance) {
      const memory = performance.memory;
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        percentage: memory.usedJSHeapSize / memory.totalJSHeapSize * 100
      };
    }
    return {
      used: 0,
      total: 0,
      percentage: 0
    };
  }
  /**
   * 清理性能数据
   */
  static clearMetrics() {
    this.metrics = [];
    console.log("性能指标已清理");
  }
  /**
   * 导出性能报告
   */
  static exportPerformanceReport() {
    const stats = this.getPerformanceStats();
    const memoryUsage = this.getMemoryUsage();
    const report = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      performanceStats: stats,
      memoryUsage,
      recentMetrics: this.metrics.slice(-10)
      // 最近10条记录
    };
    return JSON.stringify(report, null, 2);
  }
}

class DataBackupManager {
  static BACKUP_VERSION = "1.0.0";
  static BACKUP_KEY = "workspace_backup_data";
  static AUTO_BACKUP_INTERVAL = 5 * 60 * 1e3;
  // 5分钟
  static MAX_BACKUP_HISTORY = 10;
  static autoBackupTimer = null;
  /**
   * 启动自动备份
   */
  static startAutoBackup() {
    if (this.autoBackupTimer) {
      clearInterval(this.autoBackupTimer);
    }
    this.autoBackupTimer = setInterval(async () => {
      try {
        await this.createBackup();
        console.log("自动备份完成");
      } catch (error) {
        console.error("自动备份失败:", error);
      }
    }, this.AUTO_BACKUP_INTERVAL);
    console.log("自动备份已启动，间隔:", this.AUTO_BACKUP_INTERVAL / 1e3, "秒");
  }
  /**
   * 停止自动备份
   */
  static stopAutoBackup() {
    if (this.autoBackupTimer) {
      clearInterval(this.autoBackupTimer);
      this.autoBackupTimer = null;
      console.log("自动备份已停止");
    }
  }
  /**
   * 创建完整备份
   */
  static async createBackup() {
    try {
      console.log("开始创建数据备份...");
      const [
        workspacesResult,
        tabSessionsResult,
        snapshotsResult,
        mappingsResult,
        activeIdResult,
        lastActiveIdsResult
      ] = await Promise.all([
        StorageManager.getWorkspaces(),
        StorageManager.getTabSessions(),
        StorageManager.getTabStateSnapshots(),
        StorageManager.getWorkspaceTabMappings(),
        StorageManager.getActiveWorkspaceId(),
        StorageManager.getLastActiveWorkspaceIds()
      ]);
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const backupData = {
        version: this.BACKUP_VERSION,
        timestamp: Date.now(),
        workspaces: workspacesResult.data,
        tabSessions: tabSessionsResult.success ? tabSessionsResult.data : [],
        tabStateSnapshots: snapshotsResult.success ? snapshotsResult.data : [],
        workspaceTabMappings: mappingsResult.success ? mappingsResult.data : [],
        activeWorkspaceId: activeIdResult.success ? activeIdResult.data : null,
        lastActiveWorkspaceIds: lastActiveIdsResult.success ? lastActiveIdsResult.data : [],
        windowMappings: this.getWindowMappings()
      };
      const saveResult = await this.saveBackup(backupData);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log("数据备份创建完成");
      return { success: true, data: backupData };
    } catch (error) {
      console.error("创建数据备份失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to create backup",
          details: error
        }
      };
    }
  }
  /**
   * 保存备份数据
   */
  static async saveBackup(backupData) {
    try {
      const existingBackups = await this.getBackupHistory();
      existingBackups.unshift(backupData);
      if (existingBackups.length > this.MAX_BACKUP_HISTORY) {
        existingBackups.splice(this.MAX_BACKUP_HISTORY);
      }
      await chrome.storage.local.set({
        [this.BACKUP_KEY]: existingBackups
      });
      return { success: true };
    } catch (error) {
      console.error("保存备份数据失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save backup",
          details: error
        }
      };
    }
  }
  /**
   * 获取备份历史
   */
  static async getBackupHistory() {
    try {
      const result = await chrome.storage.local.get(this.BACKUP_KEY);
      return result[this.BACKUP_KEY] || [];
    } catch (error) {
      console.error("获取备份历史失败:", error);
      return [];
    }
  }
  /**
   * 获取最新备份
   */
  static async getLatestBackup() {
    try {
      const backups = await this.getBackupHistory();
      const latestBackup = backups.length > 0 ? backups[0] : null;
      return { success: true, data: latestBackup };
    } catch (error) {
      console.error("获取最新备份失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get latest backup",
          details: error
        }
      };
    }
  }
  /**
   * 从备份恢复数据
   */
  static async restoreFromBackup(backupData) {
    try {
      console.log("开始从备份恢复数据...");
      let dataToRestore = backupData;
      if (!dataToRestore) {
        const latestResult = await this.getLatestBackup();
        if (!latestResult.success || !latestResult.data) {
          return {
            success: false,
            error: {
              code: ERROR_CODES.STORAGE_ERROR,
              message: "No backup data available for restore"
            }
          };
        }
        dataToRestore = latestResult.data;
      }
      await Promise.all([
        StorageManager.saveWorkspaces(dataToRestore.workspaces),
        StorageManager.saveTabSessions(dataToRestore.tabSessions),
        StorageManager.saveTabStateSnapshots(dataToRestore.tabStateSnapshots),
        StorageManager.saveWorkspaceTabMappings(dataToRestore.workspaceTabMappings),
        StorageManager.setActiveWorkspaceId(dataToRestore.activeWorkspaceId),
        StorageManager.setLastActiveWorkspaceIds(dataToRestore.lastActiveWorkspaceIds)
      ]);
      this.restoreWindowMappings(dataToRestore.windowMappings);
      console.log("数据恢复完成");
      return { success: true };
    } catch (error) {
      console.error("从备份恢复数据失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to restore from backup",
          details: error
        }
      };
    }
  }
  /**
   * 检测系统重启或崩溃后的恢复需求
   */
  static async detectAndRecover() {
    try {
      console.log("检测是否需要数据恢复...");
      const result = await chrome.storage.local.get("extension_running");
      const wasRunning = result.extension_running || false;
      if (wasRunning) {
        console.log("检测到异常关闭，开始自动恢复...");
        const restoreResult = await this.restoreFromBackup();
        if (restoreResult.success) {
          console.log("自动恢复完成");
        } else {
          console.error("自动恢复失败:", restoreResult.error);
        }
        await chrome.storage.local.remove("extension_running");
        return { success: true, data: true };
      } else {
        console.log("正常启动，无需恢复");
        return { success: true, data: false };
      }
    } catch (error) {
      console.error("检测和恢复失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to detect and recover",
          details: error
        }
      };
    }
  }
  /**
   * 标记扩展正在运行
   */
  static async markExtensionRunning() {
    try {
      await chrome.storage.local.set({ extension_running: true });
    } catch (error) {
      console.error("标记扩展运行状态失败:", error);
    }
  }
  /**
   * 标记扩展正常关闭
   */
  static async markExtensionStopped() {
    try {
      await chrome.storage.local.remove("extension_running");
    } catch (error) {
      console.error("清除扩展运行状态失败:", error);
    }
  }
  /**
   * 获取窗口映射
   */
  static getWindowMappings() {
    const mappings = {};
    return mappings;
  }
  /**
   * 恢复窗口映射
   */
  static restoreWindowMappings(mappings) {
    console.log("窗口映射恢复（暂未实现）:", mappings);
  }
  /**
   * 导出备份数据为JSON
   */
  static async exportBackupAsJson() {
    try {
      const backupResult = await this.createBackup();
      if (!backupResult.success) {
        return { success: false, error: backupResult.error };
      }
      const exportData = {
        version: this.BACKUP_VERSION,
        exportedAt: Date.now(),
        workspaces: backupResult.data.workspaces,
        settings: (await StorageManager.getSettings()).data,
        tabSessions: backupResult.data.tabSessions
      };
      return { success: true, data: JSON.stringify(exportData, null, 2) };
    } catch (error) {
      console.error("导出备份数据失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to export backup as JSON",
          details: error
        }
      };
    }
  }
}

class TabSessionManager {
  static MAX_HISTORY_ENTRIES = 10;
  static MAX_NAVIGATION_HISTORY = 5;
  /**
   * 生成唯一的会话ID
   */
  static generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  /**
   * 创建标签页会话
   */
  static async createTabSession(workspaceId, tabs) {
    try {
      const sessionId = this.generateSessionId();
      const now = Date.now();
      const enhancedTabs = tabs.map((tab) => ({
        ...tab,
        workspaceId,
        sessionId,
        lastActiveTime: tab.isActive ? now : tab.lastActiveTime || now,
        navigationHistory: tab.navigationHistory || [tab.url]
      }));
      const session = {
        id: sessionId,
        workspaceId,
        tabs: enhancedTabs,
        createdAt: now,
        lastUpdated: now,
        isActive: true
      };
      const saveResult = await this.saveTabSession(session);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`创建标签页会话: ${sessionId}, 工作区: ${workspaceId}, 标签页数量: ${tabs.length}`);
      return { success: true, data: session };
    } catch (error) {
      console.error("创建标签页会话失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to create tab session",
          details: error
        }
      };
    }
  }
  /**
   * 保存标签页会话
   */
  static async saveTabSession(session) {
    try {
      const sessionsResult = await this.getAllTabSessions();
      if (!sessionsResult.success) {
        return { success: false, error: sessionsResult.error };
      }
      const sessions = sessionsResult.data;
      const existingIndex = sessions.findIndex((s) => s.id === session.id);
      if (existingIndex >= 0) {
        sessions[existingIndex] = { ...session, lastUpdated: Date.now() };
      } else {
        sessions.push(session);
      }
      if (sessions.length > this.MAX_HISTORY_ENTRIES) {
        sessions.sort((a, b) => b.lastUpdated - a.lastUpdated);
        sessions.splice(this.MAX_HISTORY_ENTRIES);
      }
      await StorageManager.saveTabSessions(sessions);
      return { success: true };
    } catch (error) {
      console.error("保存标签页会话失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save tab session",
          details: error
        }
      };
    }
  }
  /**
   * 获取所有标签页会话
   */
  static async getAllTabSessions() {
    try {
      const result = await StorageManager.getTabSessions();
      return result;
    } catch (error) {
      console.error("获取标签页会话失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get tab sessions",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区的最新标签页会话
   */
  static async getWorkspaceLatestSession(workspaceId) {
    try {
      const sessionsResult = await this.getAllTabSessions();
      if (!sessionsResult.success) {
        return { success: false, error: sessionsResult.error };
      }
      const sessions = sessionsResult.data;
      const workspaceSessions = sessions.filter((s) => s.workspaceId === workspaceId).sort((a, b) => b.lastUpdated - a.lastUpdated);
      return {
        success: true,
        data: workspaceSessions.length > 0 ? workspaceSessions[0] : null
      };
    } catch (error) {
      console.error("获取工作区最新会话失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get workspace latest session",
          details: error
        }
      };
    }
  }
  /**
   * 创建标签页状态快照
   */
  static async createTabStateSnapshot(workspaceId, windowId, tabs) {
    try {
      const sessionId = this.generateSessionId();
      const snapshot = {
        workspaceId,
        windowId,
        tabs: tabs.map((tab) => ({
          ...tab,
          workspaceId,
          sessionId,
          lastActiveTime: tab.isActive ? Date.now() : tab.lastActiveTime || Date.now()
        })),
        timestamp: Date.now(),
        sessionId
      };
      const saveResult = await this.saveTabStateSnapshot(snapshot);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`创建标签页状态快照: ${sessionId}, 工作区: ${workspaceId}, 标签页数量: ${tabs.length}`);
      return { success: true, data: snapshot };
    } catch (error) {
      console.error("创建标签页状态快照失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to create tab state snapshot",
          details: error
        }
      };
    }
  }
  /**
   * 保存标签页状态快照
   */
  static async saveTabStateSnapshot(snapshot) {
    try {
      const snapshotsResult = await StorageManager.getTabStateSnapshots();
      if (!snapshotsResult.success) {
        return { success: false, error: snapshotsResult.error };
      }
      const snapshots = snapshotsResult.data;
      snapshots.push(snapshot);
      if (snapshots.length > this.MAX_HISTORY_ENTRIES) {
        snapshots.sort((a, b) => b.timestamp - a.timestamp);
        snapshots.splice(this.MAX_HISTORY_ENTRIES);
      }
      await StorageManager.saveTabStateSnapshots(snapshots);
      return { success: true };
    } catch (error) {
      console.error("保存标签页状态快照失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save tab state snapshot",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区的最新状态快照
   */
  static async getWorkspaceLatestSnapshot(workspaceId) {
    try {
      const snapshotsResult = await StorageManager.getTabStateSnapshots();
      if (!snapshotsResult.success) {
        return { success: false, error: snapshotsResult.error };
      }
      const snapshots = snapshotsResult.data;
      const workspaceSnapshots = snapshots.filter((s) => s.workspaceId === workspaceId).sort((a, b) => b.timestamp - a.timestamp);
      return {
        success: true,
        data: workspaceSnapshots.length > 0 ? workspaceSnapshots[0] : null
      };
    } catch (error) {
      console.error("获取工作区最新快照失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get workspace latest snapshot",
          details: error
        }
      };
    }
  }
  /**
   * 更新标签页的导航历史
   */
  static updateTabNavigationHistory(tab, newUrl) {
    const history = tab.navigationHistory || [];
    if (history[history.length - 1] !== newUrl) {
      history.push(newUrl);
      if (history.length > this.MAX_NAVIGATION_HISTORY) {
        history.shift();
      }
    }
    return {
      ...tab,
      url: newUrl,
      navigationHistory: history,
      lastActiveTime: Date.now()
    };
  }
  /**
   * 清理过期的会话和快照
   */
  static async cleanupExpiredData(maxAgeMs = 7 * 24 * 60 * 60 * 1e3) {
    try {
      const now = Date.now();
      const cutoffTime = now - maxAgeMs;
      const sessionsResult = await this.getAllTabSessions();
      if (sessionsResult.success) {
        const validSessions = sessionsResult.data.filter((s) => s.lastUpdated > cutoffTime);
        await StorageManager.saveTabSessions(validSessions);
      }
      const snapshotsResult = await StorageManager.getTabStateSnapshots();
      if (snapshotsResult.success) {
        const validSnapshots = snapshotsResult.data.filter((s) => s.timestamp > cutoffTime);
        await StorageManager.saveTabStateSnapshots(validSnapshots);
      }
      console.log("清理过期数据完成");
      return { success: true };
    } catch (error) {
      console.error("清理过期数据失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to cleanup expired data",
          details: error
        }
      };
    }
  }
}

class WorkspaceSwitchDetector {
  static currentWorkspaceId = null;
  static switchInProgress = false;
  static lastSwitchTime = 0;
  static SWITCH_DEBOUNCE_MS = 1e3;
  // 防抖时间
  /**
   * 初始化检测器
   */
  static async initialize() {
    try {
      const currentResult = await StorageManager.getActiveWorkspaceId();
      if (currentResult.success) {
        this.currentWorkspaceId = currentResult.data;
      }
      console.log("工作区切换检测器初始化完成，当前工作区:", this.currentWorkspaceId);
      return { success: true };
    } catch (error) {
      console.error("工作区切换检测器初始化失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to initialize workspace switch detector",
          details: error
        }
      };
    }
  }
  /**
   * 检测工作区切换事件
   */
  static async detectWorkspaceSwitch(newWorkspaceId) {
    try {
      const now = Date.now();
      if (this.switchInProgress || now - this.lastSwitchTime < this.SWITCH_DEBOUNCE_MS) {
        console.log("工作区切换正在进行中或过于频繁，跳过检测");
        return { success: true, data: null };
      }
      if (this.currentWorkspaceId === newWorkspaceId) {
        console.log("工作区未发生变化，跳过切换检测");
        return { success: true, data: null };
      }
      console.log(`检测到工作区切换: ${this.currentWorkspaceId} -> ${newWorkspaceId}`);
      const switchEvent = {
        type: "workspace-switch-detected",
        fromWorkspaceId: this.currentWorkspaceId,
        toWorkspaceId: newWorkspaceId,
        timestamp: now
      };
      if (this.currentWorkspaceId) {
        const tabsResult = await this.getWorkspaceTabsToSave(this.currentWorkspaceId);
        if (tabsResult.success) {
          switchEvent.tabsToSave = tabsResult.data;
        }
      }
      const restoreResult = await this.getWorkspaceTabsToRestore(newWorkspaceId);
      if (restoreResult.success) {
        switchEvent.tabsToRestore = restoreResult.data;
      }
      return { success: true, data: switchEvent };
    } catch (error) {
      console.error("检测工作区切换失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to detect workspace switch",
          details: error
        }
      };
    }
  }
  /**
   * 开始工作区切换流程
   */
  static async startWorkspaceSwitch(switchEvent) {
    try {
      console.log("开始工作区切换流程:", switchEvent);
      this.switchInProgress = true;
      this.lastSwitchTime = Date.now();
      const startEvent = {
        ...switchEvent,
        type: "workspace-switch-initiated",
        timestamp: Date.now()
      };
      await this.notifyWorkspaceSwitchEvent(startEvent);
      return { success: true };
    } catch (error) {
      console.error("开始工作区切换失败:", error);
      this.switchInProgress = false;
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to start workspace switch",
          details: error
        }
      };
    }
  }
  /**
   * 完成工作区切换流程
   */
  static async completeWorkspaceSwitch(switchEvent, success = true) {
    try {
      console.log("完成工作区切换流程:", switchEvent, "成功:", success);
      if (success) {
        this.currentWorkspaceId = switchEvent.toWorkspaceId;
        const completeEvent = {
          ...switchEvent,
          type: "workspace-switch-completed",
          timestamp: Date.now()
        };
        await this.notifyWorkspaceSwitchEvent(completeEvent);
      }
      this.switchInProgress = false;
      return { success: true };
    } catch (error) {
      console.error("完成工作区切换失败:", error);
      this.switchInProgress = false;
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to complete workspace switch",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区需要保存的标签页
   */
  static async getWorkspaceTabsToSave(workspaceId) {
    try {
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }
      const workspace = workspaceResult.data;
      const relatedTabsResult = await TabManager.getWorkspaceRelatedTabs(workspace);
      if (!relatedTabsResult.success) {
        return { success: false, error: relatedTabsResult.error };
      }
      const relatedTabs = relatedTabsResult.data;
      const enhancedTabs = relatedTabs.map(
        (tab) => TabManager.enhanceTabWithWorkspaceMetadata(tab, workspaceId)
      );
      console.log(`工作区 ${workspaceId} 需要保存 ${enhancedTabs.length} 个标签页`);
      return { success: true, data: enhancedTabs };
    } catch (error) {
      console.error("获取工作区需要保存的标签页失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace tabs to save",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区需要恢复的标签页
   */
  static async getWorkspaceTabsToRestore(workspaceId) {
    try {
      const sessionResult = await TabSessionManager.getWorkspaceLatestSession(workspaceId);
      if (!sessionResult.success) {
        return { success: false, error: sessionResult.error };
      }
      const session = sessionResult.data;
      if (!session) {
        console.log(`工作区 ${workspaceId} 没有保存的会话`);
        return { success: true, data: [] };
      }
      console.log(`工作区 ${workspaceId} 需要恢复 ${session.tabs.length} 个标签页`);
      return { success: true, data: session.tabs };
    } catch (error) {
      console.error("获取工作区需要恢复的标签页失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace tabs to restore",
          details: error
        }
      };
    }
  }
  /**
   * 通知工作区切换事件
   */
  static async notifyWorkspaceSwitchEvent(event) {
    try {
      console.log("工作区切换事件通知:", event);
    } catch (error) {
      console.error("通知工作区切换事件失败:", error);
    }
  }
  /**
   * 获取当前工作区ID
   */
  static getCurrentWorkspaceId() {
    return this.currentWorkspaceId;
  }
  /**
   * 检查是否正在切换中
   */
  static isSwitchInProgress() {
    return this.switchInProgress;
  }
  /**
   * 强制重置切换状态（用于错误恢复）
   */
  static resetSwitchState() {
    this.switchInProgress = false;
    console.log("工作区切换状态已重置");
  }
}

var ErrorType = /* @__PURE__ */ ((ErrorType2) => {
  ErrorType2["STORAGE_ERROR"] = "STORAGE_ERROR";
  ErrorType2["TAB_ERROR"] = "TAB_ERROR";
  ErrorType2["WINDOW_ERROR"] = "WINDOW_ERROR";
  ErrorType2["WORKSPACE_SWITCH_ERROR"] = "WORKSPACE_SWITCH_ERROR";
  ErrorType2["PERMISSION_ERROR"] = "PERMISSION_ERROR";
  ErrorType2["NETWORK_ERROR"] = "NETWORK_ERROR";
  ErrorType2["UNKNOWN_ERROR"] = "UNKNOWN_ERROR";
  return ErrorType2;
})(ErrorType || {});
class ErrorRecoveryManager {
  static errorHistory = [];
  static MAX_ERROR_HISTORY = 50;
  static MAX_RECOVERY_ATTEMPTS = 3;
  static recoveryStrategies = [];
  /**
   * 初始化错误恢复管理器
   */
  static initialize() {
    this.setupRecoveryStrategies();
    this.setupGlobalErrorHandlers();
    console.log("错误恢复管理器已初始化");
  }
  /**
   * 设置恢复策略
   */
  static setupRecoveryStrategies() {
    this.recoveryStrategies = [
      {
        name: "storage_recovery",
        description: "存储错误恢复",
        canRecover: (error) => error.type === "STORAGE_ERROR" /* STORAGE_ERROR */,
        execute: async () => {
          console.log("执行存储错误恢复...");
          const backupResult = await DataBackupManager.getLatestBackup();
          if (backupResult.success && backupResult.data) {
            return await DataBackupManager.restoreFromBackup(backupResult.data);
          }
          return { success: false, error: { code: ERROR_CODES.STORAGE_ERROR, message: "No backup available" } };
        }
      },
      {
        name: "window_recovery",
        description: "窗口错误恢复",
        canRecover: (error) => error.type === "WINDOW_ERROR" /* WINDOW_ERROR */,
        execute: async () => {
          console.log("执行窗口错误恢复...");
          await WindowManager.cleanupInvalidWindows();
          await WindowManager.ensureWindowsHidden();
          return { success: true };
        }
      },
      {
        name: "workspace_switch_recovery",
        description: "工作区切换错误恢复",
        canRecover: (error) => error.type === "WORKSPACE_SWITCH_ERROR" /* WORKSPACE_SWITCH_ERROR */,
        execute: async () => {
          console.log("执行工作区切换错误恢复...");
          WorkspaceSwitchDetector.resetSwitchState();
          return { success: true };
        }
      },
      {
        name: "tab_recovery",
        description: "标签页错误恢复",
        canRecover: (error) => error.type === "TAB_ERROR" /* TAB_ERROR */,
        execute: async () => {
          console.log("执行标签页错误恢复...");
          const workspacesResult = await StorageManager.getWorkspaces();
          if (workspacesResult.success) {
            const activeWorkspace = workspacesResult.data.find((w) => w.isActive);
            if (activeWorkspace) {
              const sessionResult = await TabSessionManager.getWorkspaceLatestSession(activeWorkspace.id);
              if (sessionResult.success && sessionResult.data) {
                console.log("找到可恢复的标签页会话");
              }
            }
          }
          return { success: true };
        }
      }
    ];
  }
  /**
   * 设置全局错误处理器
   */
  static setupGlobalErrorHandlers() {
    if (typeof window !== "undefined") {
      window.addEventListener("error", (event) => {
        this.handleError(
          "UNKNOWN_ERROR" /* UNKNOWN_ERROR */,
          event.error?.message || "Unknown error",
          event.error,
          "global_error_handler"
        );
      });
      window.addEventListener("unhandledrejection", (event) => {
        this.handleError(
          "UNKNOWN_ERROR" /* UNKNOWN_ERROR */,
          event.reason?.message || "Unhandled promise rejection",
          event.reason,
          "unhandled_rejection"
        );
      });
    }
  }
  /**
   * 处理错误
   */
  static async handleError(type, message, details, context) {
    const errorId = this.generateErrorId();
    const errorRecord = {
      id: errorId,
      type,
      message,
      details,
      timestamp: Date.now(),
      context,
      resolved: false,
      recoveryAttempts: 0
    };
    this.recordError(errorRecord);
    console.error(`错误处理 [${errorId}]:`, {
      type,
      message,
      context,
      details
    });
    await this.attemptRecovery(errorRecord);
    return errorId;
  }
  /**
   * 记录错误
   */
  static recordError(error) {
    this.errorHistory.push(error);
    if (this.errorHistory.length > this.MAX_ERROR_HISTORY) {
      this.errorHistory.shift();
    }
  }
  /**
   * 尝试自动恢复
   */
  static async attemptRecovery(error) {
    if (error.recoveryAttempts >= this.MAX_RECOVERY_ATTEMPTS) {
      console.warn(`错误 ${error.id} 已达到最大恢复尝试次数`);
      return;
    }
    const applicableStrategies = this.recoveryStrategies.filter(
      (strategy) => strategy.canRecover(error)
    );
    if (applicableStrategies.length === 0) {
      console.log(`错误 ${error.id} 没有适用的恢复策略`);
      return;
    }
    error.recoveryAttempts++;
    for (const strategy of applicableStrategies) {
      try {
        console.log(`尝试恢复策略: ${strategy.name} (${strategy.description})`);
        const result = await strategy.execute();
        if (result.success) {
          error.resolved = true;
          console.log(`错误 ${error.id} 已通过策略 ${strategy.name} 恢复`);
          return;
        } else {
          console.warn(`恢复策略 ${strategy.name} 失败:`, result.error);
        }
      } catch (recoveryError) {
        console.error(`恢复策略 ${strategy.name} 执行失败:`, recoveryError);
      }
    }
  }
  /**
   * 手动恢复选项
   */
  static async getManualRecoveryOptions() {
    try {
      const backups = await DataBackupManager.getBackupHistory();
      const latestBackup = backups.length > 0 ? backups[0] : null;
      return {
        canRestoreFromBackup: backups.length > 0,
        canResetWorkspaces: true,
        canClearAllData: true,
        backupCount: backups.length,
        lastBackupTime: latestBackup ? latestBackup.timestamp : null
      };
    } catch (error) {
      console.error("获取手动恢复选项失败:", error);
      return {
        canRestoreFromBackup: false,
        canResetWorkspaces: true,
        canClearAllData: true,
        backupCount: 0,
        lastBackupTime: null
      };
    }
  }
  /**
   * 执行手动恢复
   */
  static async executeManualRecovery(option) {
    try {
      console.log(`执行手动恢复: ${option}`);
      switch (option) {
        case "restore_backup":
          const backupResult = await DataBackupManager.getLatestBackup();
          if (backupResult.success && backupResult.data) {
            return await DataBackupManager.restoreFromBackup(backupResult.data);
          } else {
            return {
              success: false,
              error: {
                code: ERROR_CODES.STORAGE_ERROR,
                message: "No backup available for restore"
              }
            };
          }
        case "reset_workspaces":
          await StorageManager.saveWorkspaces([]);
          await StorageManager.setActiveWorkspaceId(null);
          await StorageManager.setLastActiveWorkspaceIds([]);
          return { success: true };
        case "clear_all":
          return await StorageManager.clearAllData();
        default:
          return {
            success: false,
            error: {
              code: ERROR_CODES.UNKNOWN_ERROR,
              message: "Unknown recovery option"
            }
          };
      }
    } catch (error) {
      console.error("执行手动恢复失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.UNKNOWN_ERROR,
          message: "Manual recovery failed",
          details: error
        }
      };
    }
  }
  /**
   * 获取错误统计
   */
  static getErrorStats() {
    const errorsByType = {};
    this.errorHistory.forEach((error) => {
      errorsByType[error.type] = (errorsByType[error.type] || 0) + 1;
    });
    return {
      totalErrors: this.errorHistory.length,
      resolvedErrors: this.errorHistory.filter((e) => e.resolved).length,
      errorsByType,
      recentErrors: this.errorHistory.slice(-10)
    };
  }
  /**
   * 生成错误ID
   */
  static generateErrorId() {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  /**
   * 清理错误历史
   */
  static clearErrorHistory() {
    this.errorHistory = [];
    console.log("错误历史已清理");
  }
  /**
   * 导出错误报告
   */
  static exportErrorReport() {
    const stats = this.getErrorStats();
    const report = {
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      errorStats: stats,
      errorHistory: this.errorHistory,
      recoveryStrategies: this.recoveryStrategies.map((s) => ({
        name: s.name,
        description: s.description
      }))
    };
    return JSON.stringify(report, null, 2);
  }
}

class TabManager {
  /**
   * 获取所有标签页信息
   */
  static async getAllTabs(includeMetadata = false) {
    try {
      const tabs = await chrome.tabs.query({});
      const tabInfos = tabs.map((tab) => {
        const baseInfo = {
          id: tab.id,
          url: tab.url || "",
          title: tab.title || "",
          favicon: tab.favIconUrl || "",
          isPinned: tab.pinned,
          isActive: tab.active,
          windowId: tab.windowId,
          isSuspended: tab.discarded || false
          // 包含挂起状态
        };
        if (includeMetadata) {
          baseInfo.originalIndex = tab.index;
          baseInfo.lastActiveTime = tab.active ? Date.now() : void 0;
          baseInfo.navigationHistory = [tab.url || ""];
        }
        return baseInfo;
      });
      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前活跃标签页
   */
  static async getActiveTab() {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "No active tab found"
          }
        };
      }
      const tab = tabs[0];
      const tabInfo = {
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId
      };
      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get active tab",
          details: error
        }
      };
    }
  }
  /**
   * 检查URL是否已在标签页中打开
   */
  static async findTabByUrl(url) {
    try {
      let tabs = await chrome.tabs.query({ url });
      if (tabs.length === 0) {
        try {
          const targetDomain = new URL(url).hostname;
          const allTabs = await chrome.tabs.query({});
          tabs = allTabs.filter((tab2) => {
            if (!tab2.url) return false;
            try {
              const tabDomain = new URL(tab2.url).hostname;
              return tabDomain === targetDomain;
            } catch {
              return false;
            }
          });
        } catch {
          return { success: true, data: null };
        }
      }
      if (tabs.length === 0) {
        return { success: true, data: null };
      }
      const tab = tabs[0];
      const tabInfo = {
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId
      };
      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to find tab by URL",
          details: error
        }
      };
    }
  }
  /**
   * 创建新标签页
   */
  static async createTab(url, pinned = false, active = true) {
    try {
      const tab = await chrome.tabs.create({
        url,
        pinned,
        active
      });
      const tabInfo = {
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId
      };
      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to create tab",
          details: error
        }
      };
    }
  }
  /**
   * 激活标签页
   */
  static async activateTab(tabId) {
    try {
      console.log(`Activating tab ${tabId}`);
      await chrome.tabs.update(tabId, { active: true });
      console.log(`Successfully activated tab ${tabId}`);
      return { success: true };
    } catch (error) {
      console.error(`Failed to activate tab ${tabId}:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to activate tab",
          details: error
        }
      };
    }
  }
  /**
   * 固定/取消固定标签页
   */
  static async pinTab(tabId, pinned) {
    try {
      console.log(`${pinned ? "Pinning" : "Unpinning"} tab ${tabId}`);
      await chrome.tabs.update(tabId, { pinned });
      console.log(`Successfully ${pinned ? "pinned" : "unpinned"} tab ${tabId}`);
      return { success: true };
    } catch (error) {
      console.error(`Failed to ${pinned ? "pin" : "unpin"} tab ${tabId}:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to pin/unpin tab",
          details: error
        }
      };
    }
  }
  /**
   * 关闭标签页
   */
  static async closeTab(tabId) {
    try {
      await chrome.tabs.remove(tabId);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to close tab",
          details: error
        }
      };
    }
  }
  /**
   * 关闭多个标签页
   */
  static async closeTabs(tabIds) {
    try {
      await chrome.tabs.remove(tabIds);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to close tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区相关的标签页
   */
  static async getWorkspaceRelatedTabs(workspace) {
    try {
      const allTabsResult = await this.getAllTabs();
      if (!allTabsResult.success) return allTabsResult;
      const allTabs = allTabsResult.data;
      const workspaceUrls = workspace.websites.map((w) => w.url);
      const relatedTabs = allTabs.filter(
        (tab) => workspaceUrls.some((url) => tab.url.startsWith(url))
      );
      return { success: true, data: relatedTabs };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace related tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取非工作区相关的标签页
   */
  static async getNonWorkspaceRelatedTabs(workspace) {
    try {
      const allTabsResult = await this.getAllTabs();
      if (!allTabsResult.success) return allTabsResult;
      const allTabs = allTabsResult.data;
      const workspaceUrls = workspace.websites.map((w) => w.url);
      const nonRelatedTabs = allTabs.filter(
        (tab) => !workspaceUrls.some((url) => tab.url.startsWith(url))
      );
      return { success: true, data: nonRelatedTabs };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get non-workspace related tabs",
          details: error
        }
      };
    }
  }
  /**
   * 检查标签页是否为用户手动打开的
   */
  static async isUserOpenedTab(tabId) {
    try {
      const tab = await chrome.tabs.get(tabId);
      return !tab.pinned;
    } catch {
      return false;
    }
  }
  /**
   * 挂起标签页（释放内存）
   */
  static async suspendTab(tabId) {
    try {
      console.log(`挂起标签页: ${tabId}`);
      await chrome.tabs.discard(tabId);
      console.log(`成功挂起标签页: ${tabId}`);
      return { success: true };
    } catch (error) {
      console.error(`挂起标签页失败: ${tabId}`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to suspend tab",
          details: error
        }
      };
    }
  }
  /**
   * 批量挂起标签页
   */
  static async suspendTabs(tabIds) {
    try {
      console.log(`批量挂起 ${tabIds.length} 个标签页`);
      const results = await Promise.allSettled(
        tabIds.map((tabId) => chrome.tabs.discard(tabId))
      );
      const failedCount = results.filter((result) => result.status === "rejected").length;
      if (failedCount > 0) {
        console.warn(`${failedCount} 个标签页挂起失败`);
      }
      console.log(`成功挂起 ${tabIds.length - failedCount} 个标签页`);
      return { success: true };
    } catch (error) {
      console.error("批量挂起标签页失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to suspend tabs",
          details: error
        }
      };
    }
  }
  /**
   * 检查标签页是否已被挂起
   */
  static async isTabSuspended(tabId) {
    try {
      const tab = await chrome.tabs.get(tabId);
      return {
        success: true,
        data: tab.discarded || false
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to check tab suspension status",
          details: error
        }
      };
    }
  }
  /**
   * 恢复挂起的标签页
   */
  static async resumeTab(tabId) {
    try {
      console.log(`恢复标签页: ${tabId}`);
      await chrome.tabs.update(tabId, { active: true });
      console.log(`成功恢复标签页: ${tabId}`);
      return { success: true };
    } catch (error) {
      console.error(`恢复标签页失败: ${tabId}`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to resume tab",
          details: error
        }
      };
    }
  }
  /**
   * 为标签页添加工作区元数据
   */
  static enhanceTabWithWorkspaceMetadata(tab, workspaceId, sessionId) {
    return {
      ...tab,
      workspaceId,
      sessionId: sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      lastActiveTime: tab.isActive ? Date.now() : tab.lastActiveTime || Date.now(),
      navigationHistory: tab.navigationHistory || [tab.url],
      originalIndex: tab.originalIndex || 0
    };
  }
  /**
   * 获取标签页的完整元数据信息
   */
  static async getTabWithMetadata(tabId) {
    try {
      const tab = await chrome.tabs.get(tabId);
      const tabInfo = {
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        isSuspended: tab.discarded || false,
        originalIndex: tab.index,
        lastActiveTime: tab.active ? Date.now() : void 0,
        navigationHistory: [tab.url || ""]
      };
      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get tab with metadata",
          details: error
        }
      };
    }
  }
  /**
   * 批量获取标签页的元数据信息
   */
  static async getTabsWithMetadata(tabIds) {
    try {
      const tabInfos = [];
      for (const tabId of tabIds) {
        try {
          const result = await this.getTabWithMetadata(tabId);
          if (result.success && result.data) {
            tabInfos.push(result.data);
          }
        } catch (error) {
          console.warn(`获取标签页 ${tabId} 元数据失败:`, error);
        }
      }
      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get tabs with metadata",
          details: error
        }
      };
    }
  }
  /**
   * 序列化标签页信息为JSON
   */
  static serializeTabInfo(tab) {
    try {
      return JSON.stringify({
        id: tab.id,
        url: tab.url,
        title: tab.title,
        favicon: tab.favicon,
        isPinned: tab.isPinned,
        isActive: tab.isActive,
        windowId: tab.windowId,
        isSuspended: tab.isSuspended,
        workspaceId: tab.workspaceId,
        originalIndex: tab.originalIndex,
        sessionId: tab.sessionId,
        lastActiveTime: tab.lastActiveTime,
        navigationHistory: tab.navigationHistory
      });
    } catch (error) {
      console.error("序列化标签页信息失败:", error);
      return "{}";
    }
  }
  /**
   * 从JSON反序列化标签页信息
   */
  static deserializeTabInfo(jsonString) {
    try {
      const data = JSON.parse(jsonString);
      return {
        id: data.id || 0,
        url: data.url || "",
        title: data.title || "",
        favicon: data.favicon || "",
        isPinned: data.isPinned || false,
        isActive: data.isActive || false,
        windowId: data.windowId || 0,
        isSuspended: data.isSuspended || false,
        workspaceId: data.workspaceId,
        originalIndex: data.originalIndex,
        sessionId: data.sessionId,
        lastActiveTime: data.lastActiveTime,
        navigationHistory: data.navigationHistory || []
      };
    } catch (error) {
      console.error("反序列化标签页信息失败:", error);
      return null;
    }
  }
  /**
   * 批量创建标签页（性能优化版本）
   */
  static async batchCreateTabs(urls, options = {}) {
    const monitoringId = PerformanceManager.startPerformanceMonitoring(`batch_create_tabs_${urls.length}`);
    try {
      console.log(`开始批量创建 ${urls.length} 个标签页`);
      const result = await PerformanceManager.batchProcessTabs(
        urls,
        async (url) => {
          return await this.createTab(url, options.pinned || false, false);
        },
        {
          batchSize: Math.min(10, Math.max(5, Math.floor(urls.length / 10))),
          // 动态批大小
          delayBetweenBatches: 50,
          maxConcurrent: 5,
          timeoutPerBatch: 1e4
        }
      );
      if (result.success) {
        const createdTabs = result.data.filter((r) => r.success).map((r) => r.data);
        PerformanceManager.endPerformanceMonitoring(monitoringId, true, createdTabs.length);
        console.log(`成功批量创建 ${createdTabs.length}/${urls.length} 个标签页`);
        return { success: true, data: createdTabs };
      } else {
        PerformanceManager.endPerformanceMonitoring(monitoringId, false, 0, result.error?.message);
        return result;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      PerformanceManager.endPerformanceMonitoring(monitoringId, false, 0, errorMessage);
      await ErrorRecoveryManager.handleError(
        ErrorType.TAB_ERROR,
        "Batch create tabs failed",
        error,
        "batchCreateTabs"
      );
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to batch create tabs",
          details: error
        }
      };
    }
  }
  /**
   * 批量挂起标签页（性能优化版本）
   */
  static async batchSuspendTabs(tabIds) {
    const monitoringId = PerformanceManager.startPerformanceMonitoring(`batch_suspend_tabs_${tabIds.length}`);
    try {
      console.log(`开始批量挂起 ${tabIds.length} 个标签页`);
      const result = await PerformanceManager.batchProcessTabs(
        tabIds,
        async (tabId) => {
          return await this.suspendTab(tabId);
        },
        {
          batchSize: 15,
          // 挂起操作可以更大的批次
          delayBetweenBatches: 30,
          maxConcurrent: 8,
          timeoutPerBatch: 5e3
        }
      );
      if (result.success) {
        PerformanceManager.endPerformanceMonitoring(monitoringId, true, tabIds.length);
        console.log(`批量挂起标签页完成`);
        return { success: true };
      } else {
        PerformanceManager.endPerformanceMonitoring(monitoringId, false, 0, result.error?.message);
        return result;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      PerformanceManager.endPerformanceMonitoring(monitoringId, false, 0, errorMessage);
      await ErrorRecoveryManager.handleError(
        ErrorType.TAB_ERROR,
        "Batch suspend tabs failed",
        error,
        "batchSuspendTabs"
      );
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to batch suspend tabs",
          details: error
        }
      };
    }
  }
  /**
   * 智能标签页管理（大量标签页优化）
   */
  static async optimizeTabsForPerformance() {
    const monitoringId = PerformanceManager.startPerformanceMonitoring("optimize_tabs_performance");
    try {
      console.log("开始智能标签页性能优化...");
      const allTabsResult = await this.getAllTabs(true);
      if (!allTabsResult.success) {
        return { success: false, error: allTabsResult.error };
      }
      const allTabs = allTabsResult.data;
      console.log(`发现 ${allTabs.length} 个标签页需要优化`);
      let suspendedCount = 0;
      let closedCount = 0;
      const now = Date.now();
      const fiveMinutesAgo = now - 5 * 60 * 1e3;
      const tabsToSuspend = allTabs.filter(
        (tab) => !tab.isActive && !tab.isPinned && !tab.isSuspended && (!tab.lastActiveTime || tab.lastActiveTime < fiveMinutesAgo)
      );
      if (tabsToSuspend.length > 0) {
        console.log(`挂起 ${tabsToSuspend.length} 个非活跃标签页`);
        const suspendResult = await this.batchSuspendTabs(tabsToSuspend.map((t) => t.id));
        if (suspendResult.success) {
          suspendedCount = tabsToSuspend.length;
        }
      }
      if (allTabs.length > 100) {
        const duplicateTabs = this.findDuplicateTabs(allTabs);
        if (duplicateTabs.length > 0) {
          console.log(`关闭 ${duplicateTabs.length} 个重复标签页`);
          const closeResult = await this.closeTabs(duplicateTabs.map((t) => t.id));
          if (closeResult.success) {
            closedCount = duplicateTabs.length;
          }
        }
      }
      const result = {
        suspendedCount,
        closedCount,
        totalProcessed: suspendedCount + closedCount
      };
      PerformanceManager.endPerformanceMonitoring(monitoringId, true, result.totalProcessed);
      console.log("智能标签页性能优化完成:", result);
      return { success: true, data: result };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      PerformanceManager.endPerformanceMonitoring(monitoringId, false, 0, errorMessage);
      await ErrorRecoveryManager.handleError(
        ErrorType.TAB_ERROR,
        "Tab performance optimization failed",
        error,
        "optimizeTabsForPerformance"
      );
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to optimize tabs for performance",
          details: error
        }
      };
    }
  }
  /**
   * 查找重复的标签页
   */
  static findDuplicateTabs(tabs) {
    const urlMap = /* @__PURE__ */ new Map();
    tabs.forEach((tab) => {
      const url = tab.url;
      if (!urlMap.has(url)) {
        urlMap.set(url, []);
      }
      urlMap.get(url).push(tab);
    });
    const duplicates = [];
    urlMap.forEach((tabGroup) => {
      if (tabGroup.length > 1) {
        tabGroup.sort((a, b) => (b.lastActiveTime || 0) - (a.lastActiveTime || 0));
        duplicates.push(...tabGroup.slice(1));
      }
    });
    return duplicates;
  }
}

class WorkspaceSwitcher {
  /**
   * 切换到指定工作区（专用窗口架构）
   */
  static async switchToWorkspace(workspaceId, options = {}) {
    const monitoringId = PerformanceManager.startPerformanceMonitoring(`workspace_switch_${workspaceId}`);
    try {
      console.log(`开始切换到工作区: ${workspaceId}`);
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        PerformanceManager.endPerformanceMonitoring(monitoringId, false, 0, workspaceResult.error?.message);
        return { success: false, error: workspaceResult.error };
      }
      const workspace = workspaceResult.data;
      const currentWorkspaceResult = await this.getCurrentWorkspace();
      const currentWorkspace = currentWorkspaceResult.success ? currentWorkspaceResult.data : null;
      const settingsResult = await StorageManager.getSettings();
      if (!settingsResult.success) {
        return { success: false, error: settingsResult.error };
      }
      const settings = settingsResult.data;
      const switchOptions = {
        closeOtherTabs: options.closeOtherTabs ?? settings.autoCloseOtherTabs,
        preserveUserOpenedTabs: options.preserveUserOpenedTabs ?? settings.preserveUserOpenedTabs,
        focusFirstTab: options.focusFirstTab ?? true
      };
      if (currentWorkspace && currentWorkspace.id !== workspaceId) {
        await this.saveAndMoveCurrentWorkspaceTabs(currentWorkspace);
      }
      await this.restoreWorkspaceTabs(workspace);
      await this.openWorkspaceWebsites(workspace);
      await StorageManager.setActiveWorkspaceId(workspaceId);
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success) {
        const workspaces = workspacesResult.data;
        workspaces.forEach((w) => {
          w.isActive = w.id === workspaceId;
        });
        await StorageManager.saveWorkspaces(workspaces);
      }
      if (switchOptions.focusFirstTab && workspace.websites.length > 0) {
        const firstWebsite = workspace.websites[0];
        const tabResult = await TabManager.findTabByUrl(firstWebsite.url);
        if (tabResult.success && tabResult.data) {
          await TabManager.activateTab(tabResult.data.id);
        }
      }
      console.log(`成功切换到工作区: ${workspace.name}`);
      PerformanceManager.endPerformanceMonitoring(monitoringId, true, workspace.websites.length);
      return { success: true };
    } catch (error) {
      console.error(`切换工作区失败:`, error);
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      PerformanceManager.endPerformanceMonitoring(monitoringId, false, 0, errorMessage);
      await ErrorRecoveryManager.handleError(
        ErrorType.WORKSPACE_SWITCH_ERROR,
        "Failed to switch workspace",
        error,
        `switchToWorkspace_${workspaceId}`
      );
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to switch workspace",
          details: error
        }
      };
    }
  }
  /**
   * 保存当前工作区的标签页状态并移动到专用窗口
   */
  static async saveAndMoveCurrentWorkspaceTabs(workspace) {
    try {
      console.log(`保存并移动工作区 ${workspace.name} 的标签页`);
      const workspaceTabsResult = await TabManager.getWorkspaceRelatedTabs(workspace);
      if (!workspaceTabsResult.success) {
        console.log(`获取工作区相关标签页失败:`, workspaceTabsResult.error);
        return { success: true };
      }
      const workspaceTabs = workspaceTabsResult.data;
      if (workspaceTabs.length === 0) {
        console.log(`工作区 ${workspace.name} 没有相关标签页需要保存`);
        return { success: true };
      }
      const enhancedTabs = workspaceTabs.map(
        (tab) => TabManager.enhanceTabWithWorkspaceMetadata(tab, workspace.id)
      );
      const sessionResult = await TabSessionManager.createTabSession(workspace.id, enhancedTabs);
      if (sessionResult.success) {
        console.log(`成功创建工作区 ${workspace.name} 的标签页会话`);
      } else {
        console.error(`创建标签页会话失败:`, sessionResult.error);
      }
      const currentWindow = await chrome.windows.getCurrent();
      const snapshotResult = await TabSessionManager.createTabStateSnapshot(
        workspace.id,
        currentWindow.id,
        enhancedTabs
      );
      if (snapshotResult.success) {
        console.log(`成功创建工作区 ${workspace.name} 的状态快照`);
      }
      const tabIds = workspaceTabs.map((tab) => tab.id);
      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        tabIds,
        workspace.id,
        workspace.name
      );
      if (moveResult.success) {
        console.log(`成功移动 ${tabIds.length} 个标签页到工作区专用窗口`);
      } else {
        console.error(`移动标签页到专用窗口失败:`, moveResult.error);
      }
      return { success: true };
    } catch (error) {
      console.error(`保存并移动当前工作区标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to save and move current workspace tabs",
          details: error
        }
      };
    }
  }
  /**
   * 恢复工作区的标签页状态
   */
  static async restoreWorkspaceTabs(workspace) {
    try {
      console.log(`恢复工作区 ${workspace.name} 的标签页状态`);
      const moveResult = await WindowManager.moveTabsFromWorkspaceWindow(workspace.id);
      let restoredFromWindow = 0;
      if (moveResult.success) {
        restoredFromWindow = moveResult.data.length;
        console.log(`从专用窗口恢复了 ${restoredFromWindow} 个标签页`);
      }
      if (restoredFromWindow === 0) {
        const sessionResult = await TabSessionManager.getWorkspaceLatestSession(workspace.id);
        if (sessionResult.success && sessionResult.data) {
          const session = sessionResult.data;
          console.log(`从会话中恢复工作区 ${workspace.name} 的 ${session.tabs.length} 个标签页`);
          await this.restoreTabsFromSession(session.tabs);
        } else {
          console.log(`工作区 ${workspace.name} 没有可恢复的会话数据`);
        }
      }
      await this.restoreTabStates(workspace);
      return { success: true };
    } catch (error) {
      console.error(`恢复工作区标签页状态失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to restore workspace tabs",
          details: error
        }
      };
    }
  }
  /**
   * 从会话数据中恢复标签页
   */
  static async restoreTabsFromSession(sessionTabs) {
    try {
      console.log(`从会话数据中恢复 ${sessionTabs.length} 个标签页`);
      for (const tabInfo of sessionTabs) {
        try {
          const existingTabResult = await TabManager.findTabByUrl(tabInfo.url);
          if (!existingTabResult.success || !existingTabResult.data) {
            const createResult = await TabManager.createTab(
              tabInfo.url,
              tabInfo.isPinned,
              false
              // 不立即激活
            );
            if (createResult.success) {
              console.log(`成功恢复标签页: ${tabInfo.title}`);
              if (tabInfo.isSuspended && createResult.data) {
                await TabManager.suspendTab(createResult.data.id);
              }
            } else {
              console.error(`恢复标签页失败 ${tabInfo.title}:`, createResult.error);
            }
          } else {
            console.log(`标签页已存在，跳过恢复: ${tabInfo.title}`);
          }
        } catch (error) {
          console.error(`恢复单个标签页失败 ${tabInfo.title}:`, error);
        }
      }
      return { success: true };
    } catch (error) {
      console.error(`从会话数据中恢复标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to restore tabs from session",
          details: error
        }
      };
    }
  }
  /**
   * 恢复标签页的状态（固定、激活等）
   */
  static async restoreTabStates(workspace) {
    try {
      console.log(`恢复工作区 ${workspace.name} 的标签页状态`);
      const workspaceTabsResult = await TabManager.getWorkspaceRelatedTabs(workspace);
      if (!workspaceTabsResult.success) {
        return { success: true };
      }
      const workspaceTabs = workspaceTabsResult.data;
      for (const tab of workspaceTabs) {
        try {
          const website = workspace.websites.find((w) => tab.url.startsWith(w.url));
          if (website && website.isPinned !== tab.isPinned) {
            await TabManager.pinTab(tab.id, website.isPinned);
            console.log(`恢复标签页固定状态: ${tab.title} -> ${website.isPinned}`);
          }
        } catch (error) {
          console.error(`恢复标签页状态失败 ${tab.title}:`, error);
        }
      }
      return { success: true };
    } catch (error) {
      console.error(`恢复标签页状态失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to restore tab states",
          details: error
        }
      };
    }
  }
  /**
   * 打开工作区中尚未打开的网站
   */
  static async openWorkspaceWebsites(workspace) {
    try {
      console.log(`检查并打开工作区 ${workspace.name} 中缺失的网站`);
      for (const website of workspace.websites) {
        try {
          const existingTabResult = await TabManager.findTabByUrl(website.url);
          if (!existingTabResult.success || !existingTabResult.data) {
            console.log(`创建新标签页: ${website.title}`);
            const newTabResult = await TabManager.createTab(website.url, false, false);
            if (newTabResult.success) {
              console.log(`成功创建标签页: ${website.title}`);
            } else {
              console.error(`创建标签页失败 ${website.title}:`, newTabResult.error);
            }
          } else {
            console.log(`标签页已存在: ${website.title}`);
          }
        } catch (error) {
          console.error(`处理网站 ${website.title} 时出错:`, error);
        }
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to open workspace websites",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前活跃的工作区
   */
  static async getCurrentWorkspace() {
    try {
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (!activeIdResult.success) {
        return { success: false, error: activeIdResult.error };
      }
      const activeId = activeIdResult.data;
      if (!activeId) {
        return { success: true, data: null };
      }
      const workspaceResult = await StorageManager.getWorkspace(activeId);
      if (!workspaceResult.success) {
        await StorageManager.setActiveWorkspaceId(null);
        return { success: true, data: null };
      }
      return { success: true, data: workspaceResult.data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get current workspace",
          details: error
        }
      };
    }
  }
  /**
   * 智能检测当前应该激活的工作区
   */
  static async detectActiveWorkspace() {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      if (workspaces.length === 0) {
        return { success: true, data: null };
      }
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) {
        return { success: true, data: null };
      }
      const activeTab = activeTabResult.data;
      const matchingWorkspace = workspaces.find(
        (workspace) => workspace.websites.some(
          (website) => activeTab.url.startsWith(website.url)
        )
      );
      return { success: true, data: matchingWorkspace || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to detect active workspace",
          details: error
        }
      };
    }
  }
  /**
   * 添加当前标签页到工作区
   */
  static async addCurrentTabToWorkspace(workspaceId) {
    try {
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) {
        return { success: false, error: activeTabResult.error };
      }
      const activeTab = activeTabResult.data;
      const addResult = await WorkspaceManager.addWebsite(
        workspaceId,
        activeTab.url,
        {
          title: activeTab.title,
          favicon: activeTab.favicon,
          pinTab: activeTab.isPinned
        }
      );
      if (addResult.success) {
        return { success: true };
      } else {
        return { success: false, error: addResult.error };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to add current tab to workspace",
          details: error
        }
      };
    }
  }
  /**
   * 快速切换到下一个工作区
   */
  static async switchToNextWorkspace() {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      if (workspaces.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: "No workspaces available"
          }
        };
      }
      const currentResult = await this.getCurrentWorkspace();
      if (!currentResult.success) {
        return { success: false, error: currentResult.error };
      }
      const currentWorkspace = currentResult.data;
      let nextIndex = 0;
      if (currentWorkspace) {
        const currentIndex = workspaces.findIndex((w) => w.id === currentWorkspace.id);
        nextIndex = (currentIndex + 1) % workspaces.length;
      }
      const nextWorkspace = workspaces[nextIndex];
      return await this.switchToWorkspace(nextWorkspace.id);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to switch to next workspace",
          details: error
        }
      };
    }
  }
}

export { COMMANDS as C, DataBackupManager as D, ErrorRecoveryManager as E, PerformanceManager as P, StorageManager as S, TabManager as T, URL_REGEX as U, WorkspaceManager as W, WorkspaceSwitcher as a, WORKSPACE_ICONS as b, WORKSPACE_COLORS as c, WORKSPACE_TEMPLATES as d, TabSessionManager as e, WindowManager as f, WorkspaceSwitchDetector as g };
