import { S as StorageManager, T as <PERSON><PERSON><PERSON><PERSON><PERSON>, a as WorkspaceSwitcher, e as TabSessionManager, f as WindowManager, P as PerformanceManager, D as DataBackupManager, E as ErrorRecoveryManager, g as WorkspaceSwitchDetector, C as COMMANDS } from './assets/workspaceSwitcher-0bf1Nzyh.js';

class TestManager {
  static testResults = [];
  /**
   * 运行所有测试
   */
  static async runAllTests() {
    console.log("开始运行所有测试...");
    this.testResults = [];
    const testSuite = {
      name: "Chrome标签页管理机制测试",
      description: "验证工作区切换、标签页保存恢复、错误处理等功能",
      tests: [],
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      totalDuration: 0
    };
    await this.testBasicFunctionality();
    await this.testWorkspaceSwitching();
    await this.testTabSessionManagement();
    await this.testWindowManagement();
    await this.testPerformanceOptimization();
    await this.testErrorRecovery();
    await this.testDataBackupRestore();
    testSuite.tests = [...this.testResults];
    testSuite.totalTests = this.testResults.length;
    testSuite.passedTests = this.testResults.filter((t) => t.success).length;
    testSuite.failedTests = this.testResults.filter((t) => !t.success).length;
    testSuite.totalDuration = this.testResults.reduce((sum, t) => sum + t.duration, 0);
    console.log("所有测试完成:", testSuite);
    return testSuite;
  }
  /**
   * 测试基础功能
   */
  static async testBasicFunctionality() {
    await this.runTest("存储管理基础功能", async () => {
      const testWorkspace = {
        id: "test-workspace-1",
        name: "测试工作区",
        icon: "🧪",
        color: "#3b82f6",
        websites: [],
        createdAt: Date.now(),
        updatedAt: Date.now(),
        isActive: false,
        order: 0
      };
      const saveResult = await StorageManager.saveWorkspaces([testWorkspace]);
      if (!saveResult.success) {
        throw new Error("保存工作区失败");
      }
      const getResult = await StorageManager.getWorkspaces();
      if (!getResult.success || getResult.data.length === 0) {
        throw new Error("获取工作区失败");
      }
      return "存储管理功能正常";
    });
    await this.runTest("标签页管理基础功能", async () => {
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        throw new Error("获取标签页失败");
      }
      return `获取到 ${allTabsResult.data.length} 个标签页`;
    });
  }
  /**
   * 测试工作区切换
   */
  static async testWorkspaceSwitching() {
    await this.runTest("工作区切换性能测试", async () => {
      const testWorkspace = {
        id: "test-workspace-switch",
        name: "切换测试工作区",
        icon: "🔄",
        color: "#10b981",
        websites: [
          {
            id: "test-site-1",
            url: "https://example.com",
            title: "Example Site",
            favicon: "",
            isPinned: false,
            addedAt: Date.now(),
            order: 0
          }
        ],
        createdAt: Date.now(),
        updatedAt: Date.now(),
        isActive: false,
        order: 0
      };
      await StorageManager.saveWorkspaces([testWorkspace]);
      const startTime = performance.now();
      const switchResult = await WorkspaceSwitcher.switchToWorkspace(testWorkspace.id);
      const endTime = performance.now();
      const duration = endTime - startTime;
      if (!switchResult.success) {
        throw new Error("工作区切换失败");
      }
      if (duration > 2e3) {
        throw new Error(`工作区切换耗时 ${duration.toFixed(2)}ms，超过2秒阈值`);
      }
      return `工作区切换成功，耗时 ${duration.toFixed(2)}ms`;
    });
  }
  /**
   * 测试标签页会话管理
   */
  static async testTabSessionManagement() {
    await this.runTest("标签页会话管理", async () => {
      const testTabs = [
        {
          id: 1,
          url: "https://test1.com",
          title: "Test Tab 1",
          favicon: "",
          isPinned: false,
          isActive: true,
          windowId: 1
        },
        {
          id: 2,
          url: "https://test2.com",
          title: "Test Tab 2",
          favicon: "",
          isPinned: true,
          isActive: false,
          windowId: 1
        }
      ];
      const sessionResult = await TabSessionManager.createTabSession("test-workspace", testTabs);
      if (!sessionResult.success) {
        throw new Error("创建标签页会话失败");
      }
      const getSessionResult = await TabSessionManager.getWorkspaceLatestSession("test-workspace");
      if (!getSessionResult.success || !getSessionResult.data) {
        throw new Error("获取标签页会话失败");
      }
      const session = getSessionResult.data;
      if (session.tabs.length !== testTabs.length) {
        throw new Error("会话标签页数量不匹配");
      }
      return `成功创建和获取标签页会话，包含 ${session.tabs.length} 个标签页`;
    });
  }
  /**
   * 测试窗口管理
   */
  static async testWindowManagement() {
    await this.runTest("专用窗口管理", async () => {
      const windowResult = await WindowManager.createWorkspaceWindow("test-workspace", "测试工作区");
      if (!windowResult.success) {
        throw new Error("创建专用窗口失败");
      }
      const windowInfo = windowResult.data;
      if (windowInfo.isVisible) {
        throw new Error("专用窗口应该被隐藏");
      }
      await WindowManager.closeWorkspaceWindow("test-workspace");
      return `成功创建和管理专用窗口 ${windowInfo.id}`;
    });
  }
  /**
   * 测试性能优化
   */
  static async testPerformanceOptimization() {
    await this.runTest("性能优化功能", async () => {
      const testUrls = Array.from({ length: 20 }, (_, i) => `https://test${i}.com`);
      const batchResult = await PerformanceManager.batchProcessTabs(
        testUrls,
        async (url) => {
          await new Promise((resolve) => setTimeout(resolve, 10));
          return { url, processed: true };
        }
      );
      if (!batchResult.success) {
        throw new Error("批处理功能失败");
      }
      const processedCount = batchResult.data.length;
      if (processedCount !== testUrls.length) {
        throw new Error(`批处理数量不匹配: 期望 ${testUrls.length}, 实际 ${processedCount}`);
      }
      return `成功批处理 ${processedCount} 个项目`;
    });
  }
  /**
   * 测试错误恢复
   */
  static async testErrorRecovery() {
    await this.runTest("错误恢复机制", async () => {
      let attemptCount = 0;
      const retryResult = await PerformanceManager.retryOperation(
        async () => {
          attemptCount++;
          if (attemptCount < 3) {
            throw new Error("模拟失败");
          }
          return "成功";
        },
        3,
        100
      );
      if (!retryResult.success) {
        throw new Error("重试机制失败");
      }
      if (attemptCount !== 3) {
        throw new Error(`重试次数不正确: 期望 3, 实际 ${attemptCount}`);
      }
      return `重试机制正常，尝试 ${attemptCount} 次后成功`;
    });
  }
  /**
   * 测试数据备份恢复
   */
  static async testDataBackupRestore() {
    await this.runTest("数据备份恢复", async () => {
      const backupResult = await DataBackupManager.createBackup();
      if (!backupResult.success) {
        throw new Error("创建备份失败");
      }
      const backups = await DataBackupManager.getBackupHistory();
      if (backups.length === 0) {
        throw new Error("备份历史为空");
      }
      const latestBackup = backups[0];
      if (!latestBackup.workspaces || !Array.isArray(latestBackup.workspaces)) {
        throw new Error("备份数据格式不正确");
      }
      return `成功创建备份，包含 ${latestBackup.workspaces.length} 个工作区`;
    });
  }
  /**
   * 运行单个测试
   */
  static async runTest(testName, testFunction) {
    const startTime = performance.now();
    try {
      console.log(`运行测试: ${testName}`);
      const message = await testFunction();
      const endTime = performance.now();
      const duration = endTime - startTime;
      const result = {
        testName,
        success: true,
        duration,
        message,
        timestamp: Date.now()
      };
      this.testResults.push(result);
      console.log(`✅ ${testName}: ${message} (${duration.toFixed(2)}ms)`);
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      const result = {
        testName,
        success: false,
        duration,
        message: errorMessage,
        details: error,
        timestamp: Date.now()
      };
      this.testResults.push(result);
      console.error(`❌ ${testName}: ${errorMessage} (${duration.toFixed(2)}ms)`);
    }
  }
  /**
   * 生成测试报告
   */
  static generateTestReport(testSuite) {
    const report = {
      summary: {
        name: testSuite.name,
        description: testSuite.description,
        totalTests: testSuite.totalTests,
        passedTests: testSuite.passedTests,
        failedTests: testSuite.failedTests,
        successRate: (testSuite.passedTests / testSuite.totalTests * 100).toFixed(2) + "%",
        totalDuration: testSuite.totalDuration.toFixed(2) + "ms"
      },
      tests: testSuite.tests.map((test) => ({
        name: test.testName,
        status: test.success ? "PASS" : "FAIL",
        duration: test.duration.toFixed(2) + "ms",
        message: test.message,
        timestamp: new Date(test.timestamp).toISOString()
      })),
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    };
    return JSON.stringify(report, null, 2);
  }
}

class DiagnosticManager {
  /**
   * 运行完整的系统诊断
   */
  static async runSystemDiagnostics() {
    console.log("开始系统诊断...");
    const diagnostics = [];
    diagnostics.push(await this.checkStorageHealth());
    diagnostics.push(await this.checkWindowManagement());
    diagnostics.push(await this.checkTabSessions());
    diagnostics.push(await this.checkBackupSystem());
    diagnostics.push(await this.checkPerformance());
    diagnostics.push(await this.checkErrorHistory());
    const performance = PerformanceManager.getPerformanceStats();
    const storage = await this.getStorageInfo();
    const errors = ErrorRecoveryManager.getErrorStats();
    const hasErrors = diagnostics.some((d) => d.status === "error");
    const hasWarnings = diagnostics.some((d) => d.status === "warning");
    const overall = hasErrors ? "error" : hasWarnings ? "warning" : "healthy";
    const recommendations = this.generateRecommendations(diagnostics, performance, errors);
    const report = {
      overall,
      timestamp: Date.now(),
      diagnostics,
      performance,
      storage,
      errors,
      recommendations
    };
    console.log("系统诊断完成:", report);
    return report;
  }
  /**
   * 检查存储健康状况
   */
  static async checkStorageHealth() {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      const settingsResult = await StorageManager.getSettings();
      const usageResult = await StorageManager.getStorageUsage();
      if (!workspacesResult.success || !settingsResult.success) {
        return {
          category: "存储系统",
          status: "error",
          message: "存储系统访问失败",
          suggestions: ["检查Chrome存储权限", "尝试重启扩展"]
        };
      }
      const workspaces = workspacesResult.data;
      let status = "healthy";
      let message = `存储系统正常，包含 ${workspaces.length} 个工作区`;
      const suggestions = [];
      if (usageResult.success) {
        const usage = usageResult.data;
        const usagePercentage = usage.used / usage.quota * 100;
        if (usagePercentage > 80) {
          status = "warning";
          message += `，存储使用率 ${usagePercentage.toFixed(1)}%`;
          suggestions.push("考虑清理过期数据");
        }
      }
      const invalidWorkspaces = workspaces.filter(
        (w) => !w.id || !w.name || !Array.isArray(w.websites)
      );
      if (invalidWorkspaces.length > 0) {
        status = "warning";
        message += `，发现 ${invalidWorkspaces.length} 个无效工作区`;
        suggestions.push("清理无效的工作区数据");
      }
      return {
        category: "存储系统",
        status,
        message,
        details: { workspaceCount: workspaces.length, usage: usageResult.data },
        suggestions: suggestions.length > 0 ? suggestions : void 0
      };
    } catch (error) {
      return {
        category: "存储系统",
        status: "error",
        message: "存储系统检查失败",
        details: error,
        suggestions: ["重启扩展", "检查Chrome权限"]
      };
    }
  }
  /**
   * 检查窗口管理
   */
  static async checkWindowManagement() {
    try {
      const windowsResult = await WindowManager.getAllWorkspaceWindows();
      if (!windowsResult.success) {
        return {
          category: "窗口管理",
          status: "error",
          message: "无法获取工作区窗口信息",
          suggestions: ["重启扩展", "检查窗口权限"]
        };
      }
      const windows = windowsResult.data;
      const visibleWindows = windows.filter((w) => w.isVisible);
      let status = "healthy";
      let message = `窗口管理正常，${windows.length} 个专用窗口`;
      const suggestions = [];
      if (visibleWindows.length > 0) {
        status = "warning";
        message += `，${visibleWindows.length} 个窗口可见`;
        suggestions.push("隐藏可见的专用窗口");
      }
      await WindowManager.cleanupInvalidWindows();
      return {
        category: "窗口管理",
        status,
        message,
        details: { totalWindows: windows.length, visibleWindows: visibleWindows.length },
        suggestions: suggestions.length > 0 ? suggestions : void 0
      };
    } catch (error) {
      return {
        category: "窗口管理",
        status: "error",
        message: "窗口管理检查失败",
        details: error,
        suggestions: ["重启扩展"]
      };
    }
  }
  /**
   * 检查标签页会话
   */
  static async checkTabSessions() {
    try {
      const sessionsResult = await TabSessionManager.getAllTabSessions();
      if (!sessionsResult.success) {
        return {
          category: "标签页会话",
          status: "error",
          message: "无法获取标签页会话",
          suggestions: ["检查存储权限"]
        };
      }
      const sessions = sessionsResult.data;
      const now = Date.now();
      const oldSessions = sessions.filter((s) => now - s.lastUpdated > 7 * 24 * 60 * 60 * 1e3);
      let status = "healthy";
      let message = `标签页会话正常，${sessions.length} 个会话`;
      const suggestions = [];
      if (oldSessions.length > 0) {
        status = "warning";
        message += `，${oldSessions.length} 个过期会话`;
        suggestions.push("清理过期的标签页会话");
      }
      return {
        category: "标签页会话",
        status,
        message,
        details: { totalSessions: sessions.length, oldSessions: oldSessions.length },
        suggestions: suggestions.length > 0 ? suggestions : void 0
      };
    } catch (error) {
      return {
        category: "标签页会话",
        status: "error",
        message: "标签页会话检查失败",
        details: error,
        suggestions: ["重启扩展"]
      };
    }
  }
  /**
   * 检查备份系统
   */
  static async checkBackupSystem() {
    try {
      const backups = await DataBackupManager.getBackupHistory();
      const latestBackupResult = await DataBackupManager.getLatestBackup();
      let status = "healthy";
      let message = `备份系统正常，${backups.length} 个备份`;
      const suggestions = [];
      if (backups.length === 0) {
        status = "warning";
        message = "没有可用的备份";
        suggestions.push("创建初始备份");
      } else if (latestBackupResult.success && latestBackupResult.data) {
        const latestBackup = latestBackupResult.data;
        const timeSinceLastBackup = Date.now() - latestBackup.timestamp;
        const hoursSinceLastBackup = timeSinceLastBackup / (1e3 * 60 * 60);
        if (hoursSinceLastBackup > 24) {
          status = "warning";
          message += `，最新备份 ${hoursSinceLastBackup.toFixed(1)} 小时前`;
          suggestions.push("备份可能过期，建议创建新备份");
        }
      }
      return {
        category: "备份系统",
        status,
        message,
        details: { backupCount: backups.length },
        suggestions: suggestions.length > 0 ? suggestions : void 0
      };
    } catch (error) {
      return {
        category: "备份系统",
        status: "error",
        message: "备份系统检查失败",
        details: error,
        suggestions: ["检查存储权限", "重启扩展"]
      };
    }
  }
  /**
   * 检查性能状况
   */
  static checkPerformance() {
    try {
      const stats = PerformanceManager.getPerformanceStats();
      const memoryUsage = PerformanceManager.getMemoryUsage();
      let status = "healthy";
      let message = `性能正常，平均响应时间 ${stats.averageDuration.toFixed(2)}ms`;
      const suggestions = [];
      if (stats.averageDuration > 1e3) {
        status = "warning";
        message = `性能较慢，平均响应时间 ${stats.averageDuration.toFixed(2)}ms`;
        suggestions.push("考虑优化标签页数量");
      }
      if (stats.successRate < 90) {
        status = "error";
        message += `，成功率 ${stats.successRate.toFixed(1)}%`;
        suggestions.push("检查错误日志");
      }
      if (memoryUsage.percentage > 80) {
        status = "warning";
        message += `，内存使用率 ${memoryUsage.percentage.toFixed(1)}%`;
        suggestions.push("考虑重启扩展释放内存");
      }
      return {
        category: "性能监控",
        status,
        message,
        details: { stats, memoryUsage },
        suggestions: suggestions.length > 0 ? suggestions : void 0
      };
    } catch (error) {
      return {
        category: "性能监控",
        status: "error",
        message: "性能检查失败",
        details: error,
        suggestions: ["重启扩展"]
      };
    }
  }
  /**
   * 检查错误历史
   */
  static checkErrorHistory() {
    try {
      const errorStats = ErrorRecoveryManager.getErrorStats();
      let status = "healthy";
      let message = `错误处理正常，${errorStats.totalErrors} 个历史错误`;
      const suggestions = [];
      const recentErrors = errorStats.recentErrors.filter(
        (e) => Date.now() - e.timestamp < 60 * 60 * 1e3
        // 1小时内
      );
      if (recentErrors.length > 5) {
        status = "warning";
        message = `最近1小时内有 ${recentErrors.length} 个错误`;
        suggestions.push("检查错误原因");
      }
      const unresolvedErrors = errorStats.recentErrors.filter((e) => !e.resolved);
      if (unresolvedErrors.length > 0) {
        status = "warning";
        message += `，${unresolvedErrors.length} 个未解决错误`;
        suggestions.push("尝试手动恢复");
      }
      return {
        category: "错误处理",
        status,
        message,
        details: errorStats,
        suggestions: suggestions.length > 0 ? suggestions : void 0
      };
    } catch (error) {
      return {
        category: "错误处理",
        status: "error",
        message: "错误历史检查失败",
        details: error,
        suggestions: ["重启扩展"]
      };
    }
  }
  /**
   * 获取存储信息
   */
  static async getStorageInfo() {
    try {
      const usage = await StorageManager.getStorageUsage();
      return usage.success ? usage.data : null;
    } catch (error) {
      return { error: error.message };
    }
  }
  /**
   * 生成建议
   */
  static generateRecommendations(diagnostics, performance, errors) {
    const recommendations = [];
    diagnostics.forEach((diagnostic) => {
      if (diagnostic.suggestions) {
        recommendations.push(...diagnostic.suggestions);
      }
    });
    if (performance.averageDuration > 2e3) {
      recommendations.push("工作区切换时间过长，考虑减少标签页数量");
    }
    if (performance.slowOperations.length > 0) {
      recommendations.push("发现慢操作，建议优化性能");
    }
    if (errors.totalErrors > 50) {
      recommendations.push("错误数量较多，建议清理错误历史");
    }
    return [...new Set(recommendations)];
  }
  /**
   * 运行快速健康检查
   */
  static async runQuickHealthCheck() {
    const criticalIssues = [];
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        criticalIssues.push("存储系统无法访问");
      }
      const stats = PerformanceManager.getPerformanceStats();
      if (stats.successRate < 80) {
        criticalIssues.push(`操作成功率过低: ${stats.successRate.toFixed(1)}%`);
      }
      const errorStats = ErrorRecoveryManager.getErrorStats();
      const recentErrors = errorStats.recentErrors.filter(
        (e) => Date.now() - e.timestamp < 30 * 60 * 1e3
        // 30分钟内
      );
      if (recentErrors.length > 3) {
        criticalIssues.push(`最近30分钟内有 ${recentErrors.length} 个错误`);
      }
      const status = criticalIssues.length > 0 ? "error" : "healthy";
      const message = status === "healthy" ? "系统运行正常" : `发现 ${criticalIssues.length} 个关键问题`;
      return { status, message, criticalIssues };
    } catch (error) {
      return {
        status: "error",
        message: "健康检查失败",
        criticalIssues: ["无法执行健康检查"]
      };
    }
  }
  /**
   * 生成诊断报告
   */
  static generateDiagnosticReport(report) {
    const reportData = {
      summary: {
        overall: report.overall,
        timestamp: new Date(report.timestamp).toISOString(),
        diagnosticsCount: report.diagnostics.length,
        healthyCount: report.diagnostics.filter((d) => d.status === "healthy").length,
        warningCount: report.diagnostics.filter((d) => d.status === "warning").length,
        errorCount: report.diagnostics.filter((d) => d.status === "error").length
      },
      diagnostics: report.diagnostics,
      performance: report.performance,
      storage: report.storage,
      errors: report.errors,
      recommendations: report.recommendations
    };
    return JSON.stringify(reportData, null, 2);
  }
}

class BackgroundService {
  constructor() {
    this.init();
  }
  /**
   * 初始化后台服务
   */
  async init() {
    this.initializeErrorHandlingAndPerformance();
    await this.initializeDataRecovery();
    await this.setupSidePanel();
    await this.initializeWorkspaceSwitchDetector();
    this.setupCommandListeners();
    this.setupTabListeners();
    this.setupWindowListeners();
    this.setupStorageListeners();
    await this.initializeDefaultData();
    this.setupDataBackupAndCleanup();
    await DataBackupManager.markExtensionRunning();
    console.log("WorkSpace Pro background service initialized");
  }
  /**
   * 设置侧边栏
   */
  async setupSidePanel() {
    try {
      await chrome.sidePanel.setPanelBehavior({
        openPanelOnActionClick: true
      });
    } catch (error) {
      console.error("Failed to setup side panel:", error);
    }
  }
  /**
   * 初始化错误处理和性能监控
   */
  initializeErrorHandlingAndPerformance() {
    try {
      ErrorRecoveryManager.initialize();
      console.log("错误处理和性能监控已初始化");
    } catch (error) {
      console.error("初始化错误处理和性能监控失败:", error);
    }
  }
  /**
   * 初始化数据恢复
   */
  async initializeDataRecovery() {
    try {
      console.log("开始数据恢复检测...");
      const recoveryResult = await DataBackupManager.detectAndRecover();
      if (recoveryResult.success) {
        if (recoveryResult.data) {
          console.log("检测到异常关闭，已自动恢复数据");
          this.showNotification("数据已自动恢复", "🔄");
        } else {
          console.log("正常启动，无需数据恢复");
        }
      } else {
        console.error("数据恢复检测失败:", recoveryResult.error);
      }
    } catch (error) {
      console.error("初始化数据恢复时出错:", error);
    }
  }
  /**
   * 初始化工作区切换检测器
   */
  async initializeWorkspaceSwitchDetector() {
    try {
      const result = await WorkspaceSwitchDetector.initialize();
      if (result.success) {
        console.log("工作区切换检测器初始化成功");
      } else {
        console.error("工作区切换检测器初始化失败:", result.error);
      }
    } catch (error) {
      console.error("初始化工作区切换检测器时出错:", error);
    }
  }
  /**
   * 设置数据备份和定期清理
   */
  setupDataBackupAndCleanup() {
    DataBackupManager.startAutoBackup();
    setInterval(async () => {
      try {
        await TabSessionManager.cleanupExpiredData();
        console.log("定期清理过期数据完成");
      } catch (error) {
        console.error("定期清理过期数据失败:", error);
      }
    }, 60 * 60 * 1e3);
    setInterval(async () => {
      try {
        await WindowManager.cleanupInvalidWindows();
        await WindowManager.ensureWindowsHidden();
        console.log("定期窗口维护完成");
      } catch (error) {
        console.error("定期窗口维护失败:", error);
      }
    }, 10 * 60 * 1e3);
    setInterval(async () => {
      try {
        const optimizeResult = await TabManager.optimizeTabsForPerformance();
        if (optimizeResult.success) {
          const stats = optimizeResult.data;
          if (stats.totalProcessed > 0) {
            console.log(`性能优化完成: 挂起 ${stats.suspendedCount} 个标签页, 关闭 ${stats.closedCount} 个重复标签页`);
            this.showNotification(`性能优化: 处理了 ${stats.totalProcessed} 个标签页`, "⚡");
          }
        }
      } catch (error) {
        console.error("定期性能优化失败:", error);
      }
    }, 30 * 60 * 1e3);
    chrome.runtime.onSuspend.addListener(async () => {
      try {
        console.log("扩展即将挂起，执行清理操作...");
        await DataBackupManager.createBackup();
        await DataBackupManager.markExtensionStopped();
        DataBackupManager.stopAutoBackup();
        console.log("扩展清理完成");
      } catch (error) {
        console.error("扩展清理失败:", error);
      }
    });
  }
  /**
   * 设置命令监听器
   */
  setupCommandListeners() {
    chrome.commands.onCommand.addListener(async (command) => {
      console.log("Command received:", command);
      try {
        switch (command) {
          case COMMANDS.SWITCH_WORKSPACE_1:
            await this.switchToWorkspaceByIndex(0);
            break;
          case COMMANDS.SWITCH_WORKSPACE_2:
            await this.switchToWorkspaceByIndex(1);
            break;
          case COMMANDS.SWITCH_WORKSPACE_3:
            await this.switchToWorkspaceByIndex(2);
            break;
          case COMMANDS.TOGGLE_SIDEPANEL:
            await this.toggleSidePanel();
            break;
          case "run-tests":
            await this.runSystemTests();
            break;
          case "run-diagnostics":
            await this.runSystemDiagnostics();
            break;
          case "export-debug-info":
            await this.exportDebugInfo();
            break;
          default:
            console.log("Unknown command:", command);
        }
      } catch (error) {
        console.error("Error handling command:", command, error);
      }
    });
  }
  /**
   * 设置标签页监听器
   */
  setupTabListeners() {
    chrome.tabs.onActivated.addListener(async (activeInfo) => {
      try {
        const detectedResult = await WorkspaceSwitcher.detectActiveWorkspace();
        if (detectedResult.success && detectedResult.data) {
          const currentWorkspaceId = WorkspaceSwitchDetector.getCurrentWorkspaceId();
          if (currentWorkspaceId !== detectedResult.data.id) {
            await this.handleWorkspaceSwitch(detectedResult.data.id);
          }
        }
      } catch (error) {
        console.error("Error handling tab activation:", error);
      }
    });
    chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
      if (changeInfo.status === "complete" && tab.url) {
        try {
          const detectedResult = await WorkspaceSwitcher.detectActiveWorkspace();
          if (detectedResult.success && detectedResult.data) {
            const currentWorkspaceId = WorkspaceSwitchDetector.getCurrentWorkspaceId();
            if (currentWorkspaceId !== detectedResult.data.id) {
              await this.handleWorkspaceSwitch(detectedResult.data.id);
            }
          }
          console.log("Tab updated:", tab.url);
        } catch (error) {
          console.error("Error handling tab update:", error);
        }
      }
    });
    chrome.tabs.onCreated.addListener(async (tab) => {
      try {
        console.log("Tab created:", tab.url);
        const currentWorkspaceId = WorkspaceSwitchDetector.getCurrentWorkspaceId();
        if (currentWorkspaceId && tab.id) {
          console.log(`新标签页 ${tab.id} 可能需要关联到工作区 ${currentWorkspaceId}`);
        }
      } catch (error) {
        console.error("Error handling tab creation:", error);
      }
    });
    chrome.tabs.onRemoved.addListener(async (tabId, removeInfo) => {
      try {
        console.log("Tab removed:", tabId);
      } catch (error) {
        console.error("Error handling tab removal:", error);
      }
    });
  }
  /**
   * 设置窗口事件监听器
   */
  setupWindowListeners() {
    chrome.windows.onFocusChanged.addListener(async (windowId) => {
      try {
        if (windowId === chrome.windows.WINDOW_ID_NONE) {
          return;
        }
        const workspaceId = WindowManager.getWindowWorkspaceId(windowId);
        if (workspaceId) {
          console.log(`工作区专用窗口 ${windowId} 获得焦点，重新隐藏`);
          await WindowManager.hideWorkspaceWindow(windowId);
        }
      } catch (error) {
        console.error("处理窗口焦点变化失败:", error);
      }
    });
    chrome.windows.onCreated.addListener(async (window) => {
      try {
        console.log("新窗口创建:", window.id);
      } catch (error) {
        console.error("处理窗口创建失败:", error);
      }
    });
    chrome.windows.onRemoved.addListener(async (windowId) => {
      try {
        console.log("窗口移除:", windowId);
        const workspaceId = WindowManager.getWindowWorkspaceId(windowId);
        if (workspaceId) {
          console.log(`工作区专用窗口 ${windowId} 被移除，清理映射关系`);
        }
      } catch (error) {
        console.error("处理窗口移除失败:", error);
      }
    });
  }
  /**
   * 设置存储监听器
   */
  setupStorageListeners() {
    StorageManager.onChanged((changes) => {
      console.log("Storage changed:", changes);
      this.notifySidePanelUpdate(changes);
    });
  }
  /**
   * 初始化默认数据
   */
  async initializeDefaultData() {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success && workspacesResult.data.length === 0) {
        console.log("No workspaces found, creating default workspace templates");
      }
    } catch (error) {
      console.error("Error initializing default data:", error);
    }
  }
  /**
   * 处理工作区切换
   */
  async handleWorkspaceSwitch(newWorkspaceId) {
    try {
      if (WorkspaceSwitchDetector.isSwitchInProgress()) {
        console.log("工作区切换正在进行中，跳过新的切换请求");
        return;
      }
      const detectResult = await WorkspaceSwitchDetector.detectWorkspaceSwitch(newWorkspaceId);
      if (!detectResult.success || !detectResult.data) {
        return;
      }
      const switchEvent = detectResult.data;
      console.log("检测到工作区切换事件:", switchEvent);
      const startResult = await WorkspaceSwitchDetector.startWorkspaceSwitch(switchEvent);
      if (!startResult.success) {
        console.error("开始工作区切换失败:", startResult.error);
        return;
      }
      const switchResult = await WorkspaceSwitcher.switchToWorkspace(newWorkspaceId, {
        closeOtherTabs: false,
        focusFirstTab: false
      });
      await WorkspaceSwitchDetector.completeWorkspaceSwitch(switchEvent, switchResult.success);
      if (switchResult.success) {
        const workspaceResult = await StorageManager.getWorkspace(newWorkspaceId);
        if (workspaceResult.success) {
          const workspace = workspaceResult.data;
          this.showNotification(`切换到工作区: ${workspace.name}`, workspace.icon);
        }
      } else {
        console.error("工作区切换失败:", switchResult.error);
        this.showNotification("工作区切换失败", "❌");
      }
    } catch (error) {
      console.error("处理工作区切换时出错:", error);
      WorkspaceSwitchDetector.resetSwitchState();
    }
  }
  /**
   * 根据索引切换工作区
   */
  async switchToWorkspaceByIndex(index) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.error("Failed to get workspaces:", workspacesResult.error);
        return;
      }
      const workspaces = workspacesResult.data;
      if (index >= 0 && index < workspaces.length) {
        const workspace = workspaces[index];
        await this.handleWorkspaceSwitch(workspace.id);
      } else {
        console.log(`No workspace at index ${index}`);
      }
    } catch (error) {
      console.error("Error switching workspace by index:", error);
    }
  }
  /**
   * 切换侧边栏显示状态
   */
  async toggleSidePanel() {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length > 0) {
        const tabId = tabs[0].id;
        console.log("Toggle side panel for tab:", tabId);
      }
    } catch (error) {
      console.error("Error toggling side panel:", error);
    }
  }
  /**
   * 显示通知
   */
  showNotification(message, icon) {
    try {
      chrome.notifications.create({
        type: "basic",
        iconUrl: "icons/icon-48.png",
        title: "WorkSpace Pro",
        message: `${icon || "🚀"} ${message}`
      });
      console.log("Notification shown:", message);
    } catch (error) {
      console.error("Error showing notification:", error);
    }
  }
  /**
   * 运行系统测试
   */
  async runSystemTests() {
    try {
      console.log("开始运行系统测试...");
      this.showNotification("开始系统测试", "🧪");
      const testSuite = await TestManager.runAllTests();
      const report = TestManager.generateTestReport(testSuite);
      console.log("系统测试完成:", testSuite);
      console.log("测试报告:", report);
      const successRate = (testSuite.passedTests / testSuite.totalTests * 100).toFixed(1);
      this.showNotification(
        `测试完成: ${testSuite.passedTests}/${testSuite.totalTests} 通过 (${successRate}%)`,
        testSuite.failedTests === 0 ? "✅" : "⚠️"
      );
    } catch (error) {
      console.error("运行系统测试失败:", error);
      this.showNotification("系统测试失败", "❌");
    }
  }
  /**
   * 运行系统诊断
   */
  async runSystemDiagnostics() {
    try {
      console.log("开始系统诊断...");
      this.showNotification("开始系统诊断", "🔍");
      const report = await DiagnosticManager.runSystemDiagnostics();
      const diagnosticReport = DiagnosticManager.generateDiagnosticReport(report);
      console.log("系统诊断完成:", report);
      console.log("诊断报告:", diagnosticReport);
      const statusIcon = report.overall === "healthy" ? "✅" : report.overall === "warning" ? "⚠️" : "❌";
      this.showNotification(`系统诊断完成: ${report.overall}`, statusIcon);
    } catch (error) {
      console.error("运行系统诊断失败:", error);
      this.showNotification("系统诊断失败", "❌");
    }
  }
  /**
   * 导出调试信息
   */
  async exportDebugInfo() {
    try {
      console.log("导出调试信息...");
      const debugInfo = {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        performance: PerformanceManager.exportPerformanceReport(),
        errors: ErrorRecoveryManager.exportErrorReport(),
        backup: await DataBackupManager.exportBackupAsJson(),
        diagnostics: await DiagnosticManager.runSystemDiagnostics()
      };
      const debugReport = JSON.stringify(debugInfo, null, 2);
      console.log("调试信息:", debugReport);
      this.showNotification("调试信息已导出到控制台", "📋");
    } catch (error) {
      console.error("导出调试信息失败:", error);
      this.showNotification("导出调试信息失败", "❌");
    }
  }
  /**
   * 通知侧边栏更新
   */
  notifySidePanelUpdate(_changes) {
    try {
      console.log("Notifying side panel of storage changes");
    } catch (error) {
      console.error("Error notifying side panel:", error);
    }
  }
}
new BackgroundService();
