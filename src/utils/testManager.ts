import { OperationResult, WorkSpace, TabInfo } from '@/types/workspace';
import { StorageManager } from './storage';
import { WorkspaceSwitcher } from './workspaceSwitcher';
import { TabManager } from './tabs';
import { WindowManager } from './windowManager';
import { TabSessionManager } from './tabSessionManager';
import { DataBackupManager } from './dataBackupManager';
import { PerformanceManager } from './performanceManager';
import { ErrorRecoveryManager } from './errorRecoveryManager';
import { ERROR_CODES } from './constants';

/**
 * 测试结果接口
 */
export interface TestResult {
  testName: string;
  success: boolean;
  duration: number;
  message: string;
  details?: any;
  timestamp: number;
}

/**
 * 测试套件接口
 */
export interface TestSuite {
  name: string;
  description: string;
  tests: TestResult[];
  totalTests: number;
  passedTests: number;
  failedTests: number;
  totalDuration: number;
}

/**
 * 测试管理器
 * 负责验证标签页管理机制的各项功能
 */
export class TestManager {
  private static testResults: TestResult[] = [];

  /**
   * 运行所有测试
   */
  static async runAllTests(): Promise<TestSuite> {
    console.log('开始运行所有测试...');
    this.testResults = [];

    const testSuite: TestSuite = {
      name: 'Chrome标签页管理机制测试',
      description: '验证工作区切换、标签页保存恢复、错误处理等功能',
      tests: [],
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      totalDuration: 0
    };

    // 运行各个测试
    await this.testBasicFunctionality();
    await this.testWorkspaceSwitching();
    await this.testTabSessionManagement();
    await this.testWindowManagement();
    await this.testPerformanceOptimization();
    await this.testErrorRecovery();
    await this.testDataBackupRestore();

    // 汇总结果
    testSuite.tests = [...this.testResults];
    testSuite.totalTests = this.testResults.length;
    testSuite.passedTests = this.testResults.filter(t => t.success).length;
    testSuite.failedTests = this.testResults.filter(t => !t.success).length;
    testSuite.totalDuration = this.testResults.reduce((sum, t) => sum + t.duration, 0);

    console.log('所有测试完成:', testSuite);
    return testSuite;
  }

  /**
   * 测试基础功能
   */
  private static async testBasicFunctionality(): Promise<void> {
    // 测试存储管理
    await this.runTest('存储管理基础功能', async () => {
      const testWorkspace: WorkSpace = {
        id: 'test-workspace-1',
        name: '测试工作区',
        icon: '🧪',
        color: '#3b82f6',
        websites: [],
        createdAt: Date.now(),
        updatedAt: Date.now(),
        isActive: false,
        order: 0
      };

      // 保存工作区
      const saveResult = await StorageManager.saveWorkspaces([testWorkspace]);
      if (!saveResult.success) {
        throw new Error('保存工作区失败');
      }

      // 获取工作区
      const getResult = await StorageManager.getWorkspaces();
      if (!getResult.success || getResult.data!.length === 0) {
        throw new Error('获取工作区失败');
      }

      return '存储管理功能正常';
    });

    // 测试标签页管理
    await this.runTest('标签页管理基础功能', async () => {
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        throw new Error('获取标签页失败');
      }

      return `获取到 ${allTabsResult.data!.length} 个标签页`;
    });
  }

  /**
   * 测试工作区切换
   */
  private static async testWorkspaceSwitching(): Promise<void> {
    await this.runTest('工作区切换性能测试', async () => {
      // 创建测试工作区
      const testWorkspace: WorkSpace = {
        id: 'test-workspace-switch',
        name: '切换测试工作区',
        icon: '🔄',
        color: '#10b981',
        websites: [
          {
            id: 'test-site-1',
            url: 'https://example.com',
            title: 'Example Site',
            favicon: '',
            isPinned: false,
            addedAt: Date.now(),
            order: 0
          }
        ],
        createdAt: Date.now(),
        updatedAt: Date.now(),
        isActive: false,
        order: 0
      };

      await StorageManager.saveWorkspaces([testWorkspace]);

      // 测试切换性能
      const startTime = performance.now();
      const switchResult = await WorkspaceSwitcher.switchToWorkspace(testWorkspace.id);
      const endTime = performance.now();
      const duration = endTime - startTime;

      if (!switchResult.success) {
        throw new Error('工作区切换失败');
      }

      if (duration > 2000) {
        throw new Error(`工作区切换耗时 ${duration.toFixed(2)}ms，超过2秒阈值`);
      }

      return `工作区切换成功，耗时 ${duration.toFixed(2)}ms`;
    });
  }

  /**
   * 测试标签页会话管理
   */
  private static async testTabSessionManagement(): Promise<void> {
    await this.runTest('标签页会话管理', async () => {
      const testTabs: TabInfo[] = [
        {
          id: 1,
          url: 'https://test1.com',
          title: 'Test Tab 1',
          favicon: '',
          isPinned: false,
          isActive: true,
          windowId: 1
        },
        {
          id: 2,
          url: 'https://test2.com',
          title: 'Test Tab 2',
          favicon: '',
          isPinned: true,
          isActive: false,
          windowId: 1
        }
      ];

      // 创建会话
      const sessionResult = await TabSessionManager.createTabSession('test-workspace', testTabs);
      if (!sessionResult.success) {
        throw new Error('创建标签页会话失败');
      }

      // 获取会话
      const getSessionResult = await TabSessionManager.getWorkspaceLatestSession('test-workspace');
      if (!getSessionResult.success || !getSessionResult.data) {
        throw new Error('获取标签页会话失败');
      }

      const session = getSessionResult.data;
      if (session.tabs.length !== testTabs.length) {
        throw new Error('会话标签页数量不匹配');
      }

      return `成功创建和获取标签页会话，包含 ${session.tabs.length} 个标签页`;
    });
  }

  /**
   * 测试窗口管理
   */
  private static async testWindowManagement(): Promise<void> {
    await this.runTest('专用窗口管理', async () => {
      // 测试窗口创建和隐藏
      const windowResult = await WindowManager.createWorkspaceWindow('test-workspace', '测试工作区');
      if (!windowResult.success) {
        throw new Error('创建专用窗口失败');
      }

      const windowInfo = windowResult.data!;
      
      // 检查窗口是否被正确隐藏
      if (windowInfo.isVisible) {
        throw new Error('专用窗口应该被隐藏');
      }

      // 清理测试窗口
      await WindowManager.closeWorkspaceWindow('test-workspace');

      return `成功创建和管理专用窗口 ${windowInfo.id}`;
    });
  }

  /**
   * 测试性能优化
   */
  private static async testPerformanceOptimization(): Promise<void> {
    await this.runTest('性能优化功能', async () => {
      // 测试批处理功能
      const testUrls = Array.from({ length: 20 }, (_, i) => `https://test${i}.com`);
      
      const batchResult = await PerformanceManager.batchProcessTabs(
        testUrls,
        async (url: string) => {
          // 模拟标签页操作
          await new Promise(resolve => setTimeout(resolve, 10));
          return { url, processed: true };
        }
      );

      if (!batchResult.success) {
        throw new Error('批处理功能失败');
      }

      const processedCount = batchResult.data!.length;
      if (processedCount !== testUrls.length) {
        throw new Error(`批处理数量不匹配: 期望 ${testUrls.length}, 实际 ${processedCount}`);
      }

      return `成功批处理 ${processedCount} 个项目`;
    });
  }

  /**
   * 测试错误恢复
   */
  private static async testErrorRecovery(): Promise<void> {
    await this.runTest('错误恢复机制', async () => {
      // 测试重试机制
      let attemptCount = 0;
      const retryResult = await PerformanceManager.retryOperation(
        async () => {
          attemptCount++;
          if (attemptCount < 3) {
            throw new Error('模拟失败');
          }
          return '成功';
        },
        3,
        100
      );

      if (!retryResult.success) {
        throw new Error('重试机制失败');
      }

      if (attemptCount !== 3) {
        throw new Error(`重试次数不正确: 期望 3, 实际 ${attemptCount}`);
      }

      return `重试机制正常，尝试 ${attemptCount} 次后成功`;
    });
  }

  /**
   * 测试数据备份恢复
   */
  private static async testDataBackupRestore(): Promise<void> {
    await this.runTest('数据备份恢复', async () => {
      // 创建备份
      const backupResult = await DataBackupManager.createBackup();
      if (!backupResult.success) {
        throw new Error('创建备份失败');
      }

      // 获取备份历史
      const backups = await DataBackupManager.getBackupHistory();
      if (backups.length === 0) {
        throw new Error('备份历史为空');
      }

      const latestBackup = backups[0];
      if (!latestBackup.workspaces || !Array.isArray(latestBackup.workspaces)) {
        throw new Error('备份数据格式不正确');
      }

      return `成功创建备份，包含 ${latestBackup.workspaces.length} 个工作区`;
    });
  }

  /**
   * 运行单个测试
   */
  private static async runTest(testName: string, testFunction: () => Promise<string>): Promise<void> {
    const startTime = performance.now();
    
    try {
      console.log(`运行测试: ${testName}`);
      const message = await testFunction();
      const endTime = performance.now();
      const duration = endTime - startTime;

      const result: TestResult = {
        testName,
        success: true,
        duration,
        message,
        timestamp: Date.now()
      };

      this.testResults.push(result);
      console.log(`✅ ${testName}: ${message} (${duration.toFixed(2)}ms)`);
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      const result: TestResult = {
        testName,
        success: false,
        duration,
        message: errorMessage,
        details: error,
        timestamp: Date.now()
      };

      this.testResults.push(result);
      console.error(`❌ ${testName}: ${errorMessage} (${duration.toFixed(2)}ms)`);
    }
  }

  /**
   * 生成测试报告
   */
  static generateTestReport(testSuite: TestSuite): string {
    const report = {
      summary: {
        name: testSuite.name,
        description: testSuite.description,
        totalTests: testSuite.totalTests,
        passedTests: testSuite.passedTests,
        failedTests: testSuite.failedTests,
        successRate: ((testSuite.passedTests / testSuite.totalTests) * 100).toFixed(2) + '%',
        totalDuration: testSuite.totalDuration.toFixed(2) + 'ms'
      },
      tests: testSuite.tests.map(test => ({
        name: test.testName,
        status: test.success ? 'PASS' : 'FAIL',
        duration: test.duration.toFixed(2) + 'ms',
        message: test.message,
        timestamp: new Date(test.timestamp).toISOString()
      })),
      timestamp: new Date().toISOString()
    };

    return JSON.stringify(report, null, 2);
  }
}
