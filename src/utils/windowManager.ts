import {
  OperationResult,
  TabInfo
} from '@/types/workspace';
import { ERROR_CODES } from './constants';

/**
 * 窗口信息接口
 */
export interface WindowInfo {
  id: number;
  workspaceId: string;
  workspaceName: string;
  tabCount: number;
  isVisible: boolean;
}

/**
 * 专用窗口管理类
 * 实现 Workona 风格的专用窗口架构
 */
export class WindowManager {
  private static workspaceWindows = new Map<string, number>(); // workspaceId -> windowId
  private static windowWorkspaces = new Map<number, string>(); // windowId -> workspaceId

  /**
   * 为工作区创建专用窗口
   */
  static async createWorkspaceWindow(
    workspaceId: string, 
    workspaceName: string
  ): Promise<OperationResult<WindowInfo>> {
    try {
      console.log(`创建工作区专用窗口: ${workspaceName} (${workspaceId})`);

      // 检查是否已存在专用窗口
      const existingWindowId = this.workspaceWindows.get(workspaceId);
      if (existingWindowId) {
        try {
          // 验证窗口是否仍然存在
          const window = await chrome.windows.get(existingWindowId);
          if (window) {
            console.log(`工作区 ${workspaceName} 的专用窗口已存在: ${existingWindowId}`);
            return {
              success: true,
              data: {
                id: existingWindowId,
                workspaceId,
                workspaceName,
                tabCount: window.tabs?.length || 0,
                isVisible: window.state !== 'minimized'
              }
            };
          }
        } catch {
          // 窗口不存在，清理映射
          this.workspaceWindows.delete(workspaceId);
          this.windowWorkspaces.delete(existingWindowId);
        }
      }

      // 创建专用窗口
      const window = await chrome.windows.create({
        type: 'normal',
        state: 'normal',
        focused: false, // 不获取焦点
        width: 1200,
        height: 800,
        left: 100,
        top: 100,
        url: chrome.runtime.getURL('workspace-placeholder.html') + `?workspaceId=${workspaceId}&workspaceName=${encodeURIComponent(workspaceName)}`
      });

      // 创建后立即最小化窗口
      if (window.id) {
        try {
          await chrome.windows.update(window.id, { state: 'minimized' });
          console.log(`成功最小化工作区专用窗口: ${workspaceName} -> 窗口ID ${window.id}`);
        } catch (error) {
          console.warn(`最小化窗口失败，但窗口创建成功: ${workspaceName}`, error);
        }
      }

      if (!window.id) {
        throw new Error('Failed to create window');
      }

      // 更新映射关系
      this.workspaceWindows.set(workspaceId, window.id);
      this.windowWorkspaces.set(window.id, workspaceId);

      console.log(`成功创建工作区专用窗口: ${workspaceName} -> 窗口ID ${window.id}`);

      return {
        success: true,
        data: {
          id: window.id,
          workspaceId,
          workspaceName,
          tabCount: window.tabs?.length || 1,
          isVisible: false // 窗口默认最小化，所以不可见
        }
      };
    } catch (error) {
      console.error(`创建工作区专用窗口失败: ${workspaceName}`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: 'Failed to create workspace window',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区的专用窗口ID
   */
  static getWorkspaceWindowId(workspaceId: string): number | undefined {
    return this.workspaceWindows.get(workspaceId);
  }

  /**
   * 获取窗口对应的工作区ID
   */
  static getWindowWorkspaceId(windowId: number): string | undefined {
    return this.windowWorkspaces.get(windowId);
  }

  /**
   * 将标签页移动到工作区专用窗口
   */
  static async moveTabsToWorkspaceWindow(
    tabIds: number[],
    workspaceId: string,
    workspaceName: string
  ): Promise<OperationResult<void>> {
    try {
      if (tabIds.length === 0) {
        return { success: true };
      }

      console.log(`移动 ${tabIds.length} 个标签页到工作区 ${workspaceName}`);

      // 确保专用窗口存在
      const windowResult = await this.createWorkspaceWindow(workspaceId, workspaceName);
      if (!windowResult.success) {
        return { success: false, error: windowResult.error };
      }

      const windowId = windowResult.data!.id;

      // 移动标签页到专用窗口
      await chrome.tabs.move(tabIds, {
        windowId: windowId,
        index: -1 // 移动到窗口末尾
      });

      console.log(`成功移动 ${tabIds.length} 个标签页到工作区专用窗口 ${windowId}`);

      return { success: true };
    } catch (error) {
      console.error(`移动标签页到工作区专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move tabs to workspace window',
          details: error,
        },
      };
    }
  }

  /**
   * 将标签页从工作区专用窗口移动到主窗口
   */
  static async moveTabsFromWorkspaceWindow(
    workspaceId: string,
    targetWindowId?: number
  ): Promise<OperationResult<TabInfo[]>> {
    try {
      const windowId = this.workspaceWindows.get(workspaceId);
      if (!windowId) {
        console.log(`工作区 ${workspaceId} 没有专用窗口`);
        return { success: true, data: [] };
      }

      // 获取专用窗口中的所有标签页
      const tabs = await chrome.tabs.query({ windowId });
      
      // 过滤掉占位符页面
      const workspaceTabs = tabs.filter(tab => 
        !tab.url?.includes('workspace-placeholder.html')
      );

      if (workspaceTabs.length === 0) {
        console.log(`工作区专用窗口 ${windowId} 中没有需要移动的标签页`);
        return { success: true, data: [] };
      }

      // 确定目标窗口
      let targetWindow = targetWindowId;
      if (!targetWindow) {
        // 获取当前活跃窗口作为目标
        const currentWindow = await chrome.windows.getCurrent();
        targetWindow = currentWindow.id!;
      }

      console.log(`从工作区专用窗口 ${windowId} 移动 ${workspaceTabs.length} 个标签页到窗口 ${targetWindow}`);

      // 移动标签页
      const tabIds = workspaceTabs.map(tab => tab.id!);
      await chrome.tabs.move(tabIds, {
        windowId: targetWindow,
        index: -1
      });

      // 转换为 TabInfo 格式
      const tabInfos: TabInfo[] = workspaceTabs.map(tab => ({
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: targetWindow!,
      }));

      console.log(`成功移动 ${workspaceTabs.length} 个标签页到主窗口`);

      return { success: true, data: tabInfos };
    } catch (error) {
      console.error(`从工作区专用窗口移动标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move tabs from workspace window',
          details: error,
        },
      };
    }
  }

  /**
   * 关闭工作区专用窗口
   */
  static async closeWorkspaceWindow(workspaceId: string): Promise<OperationResult<void>> {
    try {
      const windowId = this.workspaceWindows.get(workspaceId);
      if (!windowId) {
        console.log(`工作区 ${workspaceId} 没有专用窗口需要关闭`);
        return { success: true };
      }

      console.log(`关闭工作区专用窗口: ${windowId}`);

      // 先移动所有标签页到主窗口
      await this.moveTabsFromWorkspaceWindow(workspaceId);

      // 关闭专用窗口
      await chrome.windows.remove(windowId);

      // 清理映射关系
      this.workspaceWindows.delete(workspaceId);
      this.windowWorkspaces.delete(windowId);

      console.log(`成功关闭工作区专用窗口: ${windowId}`);

      return { success: true };
    } catch (error) {
      console.error(`关闭工作区专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: 'Failed to close workspace window',
          details: error,
        },
      };
    }
  }

  /**
   * 获取所有工作区专用窗口信息
   */
  static async getAllWorkspaceWindows(): Promise<OperationResult<WindowInfo[]>> {
    try {
      const windowInfos: WindowInfo[] = [];

      for (const [workspaceId, windowId] of this.workspaceWindows.entries()) {
        try {
          const window = await chrome.windows.get(windowId, { populate: true });
          const workspaceName = this.windowWorkspaces.get(windowId) || 'Unknown';
          
          windowInfos.push({
            id: windowId,
            workspaceId,
            workspaceName,
            tabCount: window.tabs?.length || 0,
            isVisible: window.state !== 'minimized'
          });
        } catch {
          // 窗口不存在，清理映射
          this.workspaceWindows.delete(workspaceId);
          this.windowWorkspaces.delete(windowId);
        }
      }

      return { success: true, data: windowInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: 'Failed to get workspace windows',
          details: error,
        },
      };
    }
  }

  /**
   * 更新窗口标题
   */
  static async updateWindowTitle(
    workspaceId: string, 
    workspaceName: string, 
    tabCount: number
  ): Promise<OperationResult<void>> {
    try {
      const windowId = this.workspaceWindows.get(workspaceId);
      if (!windowId) {
        return { success: true }; // 窗口不存在，忽略
      }

      // 通过更新占位符页面的标题来间接更新窗口标题
      const tabs = await chrome.tabs.query({ windowId });
      const placeholderTab = tabs.find(tab => 
        tab.url?.includes('workspace-placeholder.html')
      );

      if (placeholderTab) {
        const newUrl = chrome.runtime.getURL('workspace-placeholder.html') + 
          `?workspaceId=${workspaceId}&workspaceName=${encodeURIComponent(workspaceName)}&tabCount=${tabCount}`;
        
        await chrome.tabs.update(placeholderTab.id!, { url: newUrl });
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: 'Failed to update window title',
          details: error,
        },
      };
    }
  }
}
