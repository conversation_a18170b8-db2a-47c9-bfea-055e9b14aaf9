// WorkSpace Pro - 工作区专用窗口脚本
// 从URL参数获取工作区信息
function getUrlParams() {
    const params = new URLSearchParams(window.location.search);
    return {
        workspaceId: params.get('workspaceId') || '',
        workspaceName: params.get('workspaceName') || '工作区专用窗口',
        tabCount: parseInt(params.get('tabCount') || '0')
    };
}

// 更新页面内容
function updateContent() {
    const params = getUrlParams();
    
    // 更新工作区名称
    const nameElement = document.getElementById('workspaceName');
    if (nameElement) {
        nameElement.textContent = params.workspaceName;
        document.title = `WorkSpace Pro - ${params.workspaceName}`;
    }

    // 更新标签页数量
    const countElement = document.getElementById('tabCount');
    if (countElement) {
        countElement.textContent = params.tabCount.toString();
    }

    // 根据工作区名称设置图标（简单的映射）
    const iconElement = document.getElementById('workspaceIcon');
    if (iconElement) {
        const name = params.workspaceName.toLowerCase();
        if (name.includes('ai') || name.includes('智能')) {
            iconElement.textContent = '🤖';
        } else if (name.includes('开发') || name.includes('dev')) {
            iconElement.textContent = '💻';
        } else if (name.includes('设计') || name.includes('design')) {
            iconElement.textContent = '🎨';
        } else if (name.includes('研究') || name.includes('research')) {
            iconElement.textContent = '🔬';
        } else if (name.includes('生产力') || name.includes('productivity')) {
            iconElement.textContent = '⚡';
        } else {
            iconElement.textContent = '🚀';
        }
    }
}

// 监听URL变化（用于动态更新）
window.addEventListener('popstate', updateContent);

// 初始化
updateContent();

// 更新标签页计数的函数 - 增强调试版本
async function updateTabCount() {
    console.log('[DEBUG] updateTabCount: 开始更新标签页计数...');
    
    try {
        console.log('[DEBUG] updateTabCount: 检查Chrome API可用性');
        
        if (!chrome) {
            console.error('[DEBUG] updateTabCount: Chrome API 不可用');
            return;
        }
        
        if (!chrome.tabs) {
            console.error('[DEBUG] updateTabCount: Chrome tabs API 不可用');
            return;
        }

        console.log('[DEBUG] updateTabCount: 调用 chrome.tabs.query({ currentWindow: true })');
        const tabs = await chrome.tabs.query({ currentWindow: true });
        console.log('[DEBUG] updateTabCount: chrome.tabs.query 返回结果:', {
            totalTabs: tabs.length,
            tabs: tabs.map(tab => ({ id: tab.id, title: tab.title, url: tab.url }))
        });

        // 更精确地过滤掉占位符页面，但保留其他扩展页面和普通网页
        console.log('[DEBUG] updateTabCount: 开始过滤标签页');
        const workspaceTabs = tabs.filter(tab => {
            const url = tab.url || '';
            const isValid = !url.includes('workspace-placeholder.html');
            
            if (!isValid) {
                console.log('[DEBUG] updateTabCount: 过滤掉占位符标签页:', { id: tab.id, title: tab.title, url });
            }
            
            return isValid;
        });

        console.log('[DEBUG] updateTabCount: 过滤后的工作区标签页:', {
            filteredCount: workspaceTabs.length,
            tabs: workspaceTabs.map(tab => ({ id: tab.id, title: tab.title, url: tab.url }))
        });

        // 更新页面标题以反映标签页数量
        const params = getUrlParams();
        const newTitle = `WorkSpace Pro - ${params.workspaceName} (${workspaceTabs.length} 标签页)`;
        if (document.title !== newTitle) {
            document.title = newTitle;
            console.log('[DEBUG] updateTabCount: 页面标题已更新:', newTitle);
        }

        console.log(`[DEBUG] updateTabCount: 工作区专用窗口 ${params.workspaceName} 当前标签页数量: ${workspaceTabs.length}`);

        // 同时更新标签页列表，确保数据同步
        if (typeof allTabs !== 'undefined' && allTabs.length !== workspaceTabs.length) {
            console.log('[DEBUG] updateTabCount: 标签页数量不同步，重新加载列表');
            await loadTabsList();
        }
        
        console.log('[DEBUG] updateTabCount: 标签页计数更新完成');
    } catch (error) {
        console.error('[DEBUG] updateTabCount: 更新标签页计数时出错:', error);
        console.error('[DEBUG] updateTabCount: 错误详情:', {
            name: error.name,
            message: error.message,
            stack: error.stack
        });
    }
}

// 立即更新一次
updateTabCount();

// 定期检查标签页数量变化（减少检查频率以提高性能）
setInterval(updateTabCount, 2000);

// 监听标签页变化事件以实现实时更新
if (chrome && chrome.tabs) {
    console.log('[DEBUG] 注册标签页事件监听器');
    
    chrome.tabs.onCreated.addListener(() => {
        console.log('[DEBUG] 标签页创建事件触发');
        updateTabCount();
        if (typeof loadTabsList === 'function') loadTabsList();
    });
    
    chrome.tabs.onRemoved.addListener(() => {
        console.log('[DEBUG] 标签页删除事件触发');
        updateTabCount();
        if (typeof loadTabsList === 'function') loadTabsList();
    });
    
    chrome.tabs.onMoved.addListener(() => {
        console.log('[DEBUG] 标签页移动事件触发');
        updateTabCount();
        if (typeof loadTabsList === 'function') loadTabsList();
    });
    
    chrome.tabs.onAttached.addListener(() => {
        console.log('[DEBUG] 标签页附加事件触发');
        updateTabCount();
        if (typeof loadTabsList === 'function') loadTabsList();
    });
    
    chrome.tabs.onDetached.addListener(() => {
        console.log('[DEBUG] 标签页分离事件触发');
        updateTabCount();
        if (typeof loadTabsList === 'function') loadTabsList();
    });
    
    chrome.tabs.onUpdated.addListener(() => {
        console.log('[DEBUG] 标签页更新事件触发');
        if (typeof loadTabsList === 'function') loadTabsList();
    });
} else {
    console.warn('[DEBUG] Chrome tabs API 不可用，无法注册事件监听器');
}

// 全局变量
let selectedTabIds = new Set();
let allTabs = [];
let filteredTabs = [];
let searchQuery = '';

// 加载标签页列表 - 增强调试版本
async function loadTabsList() {
    console.log('[DEBUG] loadTabsList: 开始加载标签页列表...');
    
    try {
        console.log('[DEBUG] loadTabsList: 检查Chrome API可用性');
        
        if (!chrome || !chrome.tabs) {
            console.error('[DEBUG] loadTabsList: Chrome tabs API 不可用');
            return;
        }

        console.log('[DEBUG] loadTabsList: 调用 chrome.tabs.query({ currentWindow: true })');
        const tabs = await chrome.tabs.query({ currentWindow: true });
        console.log('[DEBUG] loadTabsList: chrome.tabs.query 返回结果:', {
            totalTabs: tabs.length,
            tabs: tabs.map(tab => ({ id: tab.id, title: tab.title, url: tab.url }))
        });

        console.log('[DEBUG] loadTabsList: 开始过滤标签页');
        const workspaceTabs = tabs.filter(tab => {
            const url = tab.url || '';
            const isValid = !url.includes('workspace-placeholder.html');

            if (!isValid) {
                console.log('[DEBUG] loadTabsList: 过滤掉占位符标签页:', { id: tab.id, title: tab.title, url });
            }
            return isValid;
        });

        console.log('[DEBUG] loadTabsList: 过滤后的工作区标签页:', {
            filteredCount: workspaceTabs.length,
            tabs: workspaceTabs.map(tab => ({ id: tab.id, title: tab.title, url: tab.url }))
        });

        allTabs = workspaceTabs;
        console.log('[DEBUG] loadTabsList: 更新全局 allTabs 变量，长度:', allTabs.length);
        
        applySearchFilter();

        // 始终显示标签页列表区域，即使没有标签页也显示空状态
        const tabsSection = document.getElementById('tabsSection');
        if (tabsSection) {
            tabsSection.style.display = 'block';
            console.log('[DEBUG] loadTabsList: 标签页列表区域已显示');
        } else {
            console.warn('[DEBUG] loadTabsList: 未找到 tabsSection 元素');
        }

        // 更新统计信息
        updateTabsStats();

        console.log('[DEBUG] loadTabsList: 标签页列表加载完成');
    } catch (error) {
        console.error('[DEBUG] loadTabsList: 加载标签页列表时出错:', error);
        console.error('[DEBUG] loadTabsList: 错误详情:', {
            name: error.name,
            message: error.message,
            stack: error.stack
        });
    }
}

// 应用搜索过滤
function applySearchFilter() {
    console.log('[DEBUG] applySearchFilter: 应用搜索过滤，查询词:', searchQuery);
    
    if (!searchQuery.trim()) {
        filteredTabs = [...allTabs];
        console.log('[DEBUG] applySearchFilter: 无搜索词，显示所有标签页:', filteredTabs.length);
    } else {
        const query = searchQuery.toLowerCase();
        filteredTabs = allTabs.filter(tab => {
            const title = (tab.title || '').toLowerCase();
            const url = (tab.url || '').toLowerCase();
            return title.includes(query) || url.includes(query);
        });
        console.log('[DEBUG] applySearchFilter: 搜索过滤后的标签页数量:', filteredTabs.length);
    }
    
    renderTabsList(filteredTabs);
    updateTabsStats();
}

// 更新标签页统计信息
function updateTabsStats() {
    console.log('[DEBUG] updateTabsStats: 更新统计信息');

    const totalCount = document.getElementById('totalTabsCount');
    const filteredCount = document.getElementById('filteredTabsCount');

    if (totalCount) {
        totalCount.textContent = allTabs.length.toString();
        console.log('[DEBUG] updateTabsStats: 总标签页数量已更新:', allTabs.length);
    } else {
        console.warn('[DEBUG] updateTabsStats: 未找到 totalTabsCount 元素');
    }

    if (filteredCount) {
        filteredCount.textContent = filteredTabs.length.toString();
        console.log('[DEBUG] updateTabsStats: 过滤后标签页数量已更新:', filteredTabs.length);
    } else {
        console.warn('[DEBUG] updateTabsStats: 未找到 filteredTabsCount 元素');
    }
}

// 渲染标签页列表
function renderTabsList(tabs) {
    const tabsList = document.getElementById('tabsList');
    if (!tabsList) return;

    if (tabs.length === 0) {
        const emptyMessage = searchQuery.trim() ?
            `<div class="empty-tabs">未找到匹配 "${searchQuery}" 的标签页</div>` :
            '<div class="empty-tabs">暂无存储的标签页</div>';
        tabsList.innerHTML = emptyMessage;
        return;
    }

    tabsList.innerHTML = tabs.map(tab => {
        const title = escapeHtml(tab.title || '无标题');
        const url = escapeHtml(tab.url || '');
        const favicon = tab.favIconUrl || 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path><path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path></svg>';

        // 高亮搜索关键词
        const highlightedTitle = highlightSearchTerm(title, searchQuery);
        const highlightedUrl = highlightSearchTerm(url, searchQuery);

        return `
            <div class="tab-item ${selectedTabIds.has(tab.id) ? 'selected' : ''}" data-tab-id="${tab.id}">
                <input type="checkbox" class="tab-checkbox" ${selectedTabIds.has(tab.id) ? 'checked' : ''}
                       onchange="toggleTabSelection(${tab.id})">
                <img class="tab-favicon" src="${favicon}"
                     alt="favicon" onerror="this.style.display='none'">
                <div class="tab-info">
                    <div class="tab-title">${highlightedTitle}</div>
                    <div class="tab-url">${highlightedUrl}</div>
                </div>
                <div class="tab-status">
                    ${tab.discarded ? '<span class="status-badge status-suspended">已挂起</span>' : '<span class="status-badge status-active">活跃</span>'}
                    ${tab.pinned ? '<span class="status-badge status-pinned">已固定</span>' : ''}
                </div>
                <div class="tab-actions">
                    ${tab.discarded ?
                        `<button class="btn-small btn-success" onclick="restoreTab(${tab.id})" title="恢复标签页">恢复</button>` :
                        `<button class="btn-small btn-primary" onclick="suspendTab(${tab.id})" title="挂起标签页">挂起</button>`
                    }
                    <button class="btn-small btn-danger" onclick="deleteTab(${tab.id})" title="删除标签页">删除</button>
                </div>
            </div>
        `;
    }).join('');
}

// 高亮搜索关键词
function highlightSearchTerm(text, query) {
    if (!query.trim()) return text;

    const regex = new RegExp(`(${escapeRegex(query)})`, 'gi');
    return text.replace(regex, '<mark class="search-highlight">$1</mark>');
}

// 转义正则表达式特殊字符
function escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 切换标签页选择状态
function toggleTabSelection(tabId) {
    if (selectedTabIds.has(tabId)) {
        selectedTabIds.delete(tabId);
    } else {
        selectedTabIds.add(tabId);
    }

    // 更新UI
    const tabItem = document.querySelector(`[data-tab-id="${tabId}"]`);
    if (tabItem) {
        tabItem.classList.toggle('selected', selectedTabIds.has(tabId));
    }

    updateActionButtons();
}

// 更新操作按钮状态
function updateActionButtons() {
    const hasSelection = selectedTabIds.size > 0;
    const suspendBtn = document.getElementById('suspendSelectedBtn');
    const restoreBtn = document.getElementById('restoreSelectedBtn');
    const deleteBtn = document.getElementById('deleteSelectedBtn');

    if (suspendBtn) suspendBtn.disabled = !hasSelection;
    if (restoreBtn) restoreBtn.disabled = !hasSelection;
    if (deleteBtn) deleteBtn.disabled = !hasSelection;

    // 更新全选按钮文本
    const selectAllBtn = document.getElementById('selectAllBtn');
    if (selectAllBtn) {
        if (selectedTabIds.size === filteredTabs.length && filteredTabs.length > 0) {
            selectAllBtn.textContent = '取消全选';
        } else {
            selectAllBtn.textContent = '全选';
        }
    }
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAllBtn = document.getElementById('selectAllBtn');
    if (!selectAllBtn) return;

    if (selectedTabIds.size === filteredTabs.length && filteredTabs.length > 0) {
        // 取消全选
        selectedTabIds.clear();
    } else {
        // 全选当前过滤的标签页
        selectedTabIds.clear();
        filteredTabs.forEach(tab => selectedTabIds.add(tab.id));
    }

    renderTabsList(filteredTabs);
    updateActionButtons();
}

// 搜索功能
function handleSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchQuery = searchInput.value;
        applySearchFilter();
    }
}

// 清除搜索
function clearSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.value = '';
        searchQuery = '';
        applySearchFilter();
    }
}

// 单个标签页操作
async function suspendTab(tabId) {
    try {
        await chrome.tabs.discard(tabId);
        console.log(`成功挂起标签页: ${tabId}`);
        await loadTabsList();
    } catch (error) {
        console.warn(`挂起标签页 ${tabId} 失败:`, error);
    }
}

async function restoreTab(tabId) {
    try {
        await chrome.tabs.reload(tabId);
        console.log(`成功恢复标签页: ${tabId}`);
        await loadTabsList();
    } catch (error) {
        console.warn(`恢复标签页 ${tabId} 失败:`, error);
    }
}

async function deleteTab(tabId) {
    try {
        await chrome.tabs.remove(tabId);
        console.log(`成功删除标签页: ${tabId}`);
        await loadTabsList();
    } catch (error) {
        console.warn(`删除标签页 ${tabId} 失败:`, error);
    }
}

// 挂起选中的标签页
async function suspendSelectedTabs() {
    console.log('[DEBUG] suspendSelectedTabs: 开始挂起选中的标签页');
    console.log('[DEBUG] suspendSelectedTabs: 选中的标签页数量:', selectedTabIds.size);

    if (selectedTabIds.size === 0) {
        console.log('[DEBUG] suspendSelectedTabs: 没有选中的标签页，退出');
        return;
    }

    // 检查Chrome API可用性
    if (!chrome || !chrome.tabs) {
        console.error('[DEBUG] suspendSelectedTabs: Chrome tabs API 不可用');
        alert('Chrome tabs API 不可用，无法执行挂起操作');
        return;
    }

    try {
        const tabIds = Array.from(selectedTabIds);
        console.log(`[DEBUG] suspendSelectedTabs: 准备挂起 ${tabIds.length} 个标签页:`, tabIds);

        let successCount = 0;
        let failCount = 0;

        for (const tabId of tabIds) {
            try {
                console.log(`[DEBUG] suspendSelectedTabs: 正在挂起标签页 ${tabId}`);
                await chrome.tabs.discard(tabId);
                console.log(`[DEBUG] suspendSelectedTabs: 成功挂起标签页: ${tabId}`);
                successCount++;
            } catch (error) {
                console.warn(`[DEBUG] suspendSelectedTabs: 挂起标签页 ${tabId} 失败:`, error);
                failCount++;
            }
        }

        console.log(`[DEBUG] suspendSelectedTabs: 挂起操作完成，成功: ${successCount}, 失败: ${failCount}`);

        // 清除选择并刷新列表
        selectedTabIds.clear();
        await loadTabsList();
        updateActionButtons();

        // 显示操作结果
        if (failCount > 0) {
            alert(`挂起操作完成。成功: ${successCount}, 失败: ${failCount}`);
        }

        console.log(`[DEBUG] suspendSelectedTabs: 批量挂起操作完成`);
    } catch (error) {
        console.error('[DEBUG] suspendSelectedTabs: 批量挂起标签页时出错:', error);
        alert(`挂起操作失败: ${error.message}`);
    }
}

// 恢复选中的标签页
async function restoreSelectedTabs() {
    if (selectedTabIds.size === 0) return;

    try {
        const tabIds = Array.from(selectedTabIds);
        console.log(`准备恢复 ${tabIds.length} 个标签页`);

        for (const tabId of tabIds) {
            try {
                await chrome.tabs.reload(tabId);
                console.log(`成功恢复标签页: ${tabId}`);
            } catch (error) {
                console.warn(`恢复标签页 ${tabId} 失败:`, error);
            }
        }

        // 清除选择并刷新列表
        selectedTabIds.clear();
        await loadTabsList();
        updateActionButtons();

        console.log(`批量恢复操作完成`);
    } catch (error) {
        console.error('批量恢复标签页时出错:', error);
    }
}

// 显示删除确认对话框
function showDeleteConfirmation() {
    console.log('[DEBUG] showDeleteConfirmation: 显示删除确认对话框');
    console.log('[DEBUG] showDeleteConfirmation: 选中的标签页数量:', selectedTabIds.size);

    if (selectedTabIds.size === 0) {
        console.log('[DEBUG] showDeleteConfirmation: 没有选中的标签页，退出');
        return;
    }

    const modal = document.getElementById('deleteModal');
    const message = document.getElementById('deleteMessage');

    if (modal && message) {
        message.textContent = `确定要删除选中的 ${selectedTabIds.size} 个标签页吗？此操作无法撤销。`;
        modal.style.display = 'flex';
        console.log('[DEBUG] showDeleteConfirmation: 删除确认对话框已显示');
    } else {
        console.warn('[DEBUG] showDeleteConfirmation: 未找到模态框或消息元素');
        console.warn('[DEBUG] showDeleteConfirmation: modal:', modal);
        console.warn('[DEBUG] showDeleteConfirmation: message:', message);
    }
}

// 隐藏删除确认对话框
function hideDeleteConfirmation() {
    const modal = document.getElementById('deleteModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// 删除选中的标签页
async function deleteSelectedTabs() {
    console.log('[DEBUG] deleteSelectedTabs: 开始删除选中的标签页');
    console.log('[DEBUG] deleteSelectedTabs: 选中的标签页数量:', selectedTabIds.size);

    if (selectedTabIds.size === 0) {
        console.log('[DEBUG] deleteSelectedTabs: 没有选中的标签页，退出');
        hideDeleteConfirmation();
        return;
    }

    // 检查Chrome API可用性
    if (!chrome || !chrome.tabs) {
        console.error('[DEBUG] deleteSelectedTabs: Chrome tabs API 不可用');
        alert('Chrome tabs API 不可用，无法执行删除操作');
        hideDeleteConfirmation();
        return;
    }

    try {
        const tabIds = Array.from(selectedTabIds);
        console.log(`[DEBUG] deleteSelectedTabs: 准备删除 ${tabIds.length} 个标签页:`, tabIds);

        await chrome.tabs.remove(tabIds);
        console.log(`[DEBUG] deleteSelectedTabs: 标签页删除API调用成功`);

        // 清除选择并刷新列表
        selectedTabIds.clear();
        await loadTabsList();
        updateActionButtons();
        hideDeleteConfirmation();

        console.log(`[DEBUG] deleteSelectedTabs: 批量删除操作完成`);
    } catch (error) {
        console.error('[DEBUG] deleteSelectedTabs: 批量删除标签页时出错:', error);
        alert(`删除操作失败: ${error.message}`);
        hideDeleteConfirmation();
    }
}

// 初始化事件监听器
function initializeEventListeners() {
    console.log('[DEBUG] initializeEventListeners: 开始初始化事件监听器...');

    // 搜索输入框
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', handleSearch);
        searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                clearSearch();
            }
        });
        console.log('[DEBUG] initializeEventListeners: 搜索输入框事件已绑定');
    } else {
        console.warn('[DEBUG] initializeEventListeners: 未找到搜索输入框元素');
    }

    // 清除搜索按钮
    const clearSearchBtn = document.getElementById('clearSearchBtn');
    if (clearSearchBtn) {
        clearSearchBtn.addEventListener('click', clearSearch);
        console.log('[DEBUG] initializeEventListeners: 清除搜索按钮事件已绑定');
    } else {
        console.warn('[DEBUG] initializeEventListeners: 未找到清除搜索按钮元素');
    }

    // 全选按钮
    const selectAllBtn = document.getElementById('selectAllBtn');
    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', toggleSelectAll);
        console.log('[DEBUG] initializeEventListeners: 全选按钮事件已绑定');
    } else {
        console.warn('[DEBUG] initializeEventListeners: 未找到全选按钮元素');
    }

    // 挂起选中按钮
    const suspendBtn = document.getElementById('suspendSelectedBtn');
    if (suspendBtn) {
        suspendBtn.addEventListener('click', (e) => {
            console.log('[DEBUG] 挂起选中按钮被点击');
            e.preventDefault();
            suspendSelectedTabs();
        });
        console.log('[DEBUG] initializeEventListeners: 挂起选中按钮事件已绑定');
    } else {
        console.warn('[DEBUG] initializeEventListeners: 未找到挂起选中按钮元素');
    }

    // 恢复选中按钮
    const restoreBtn = document.getElementById('restoreSelectedBtn');
    if (restoreBtn) {
        restoreBtn.addEventListener('click', (e) => {
            console.log('[DEBUG] 恢复选中按钮被点击');
            e.preventDefault();
            restoreSelectedTabs();
        });
        console.log('[DEBUG] initializeEventListeners: 恢复选中按钮事件已绑定');
    } else {
        console.warn('[DEBUG] initializeEventListeners: 未找到恢复选中按钮元素');
    }

    // 删除选中按钮
    const deleteBtn = document.getElementById('deleteSelectedBtn');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', (e) => {
            console.log('[DEBUG] 删除选中按钮被点击');
            e.preventDefault();
            showDeleteConfirmation();
        });
        console.log('[DEBUG] initializeEventListeners: 删除选中按钮事件已绑定');
    } else {
        console.warn('[DEBUG] initializeEventListeners: 未找到删除选中按钮元素');
    }

    // 确认删除按钮
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', (e) => {
            console.log('[DEBUG] 确认删除按钮被点击');
            e.preventDefault();
            deleteSelectedTabs();
        });
        console.log('[DEBUG] initializeEventListeners: 确认删除按钮事件已绑定');
    } else {
        console.warn('[DEBUG] initializeEventListeners: 未找到确认删除按钮元素');
    }

    // 取消删除按钮
    const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
    if (cancelDeleteBtn) {
        cancelDeleteBtn.addEventListener('click', (e) => {
            console.log('[DEBUG] 取消删除按钮被点击');
            e.preventDefault();
            hideDeleteConfirmation();
        });
        console.log('[DEBUG] initializeEventListeners: 取消删除按钮事件已绑定');
    } else {
        console.warn('[DEBUG] initializeEventListeners: 未找到取消删除按钮元素');
    }

    // 点击模态框背景关闭
    const modal = document.getElementById('deleteModal');
    if (modal) {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                console.log('[DEBUG] 模态框背景被点击，关闭对话框');
                hideDeleteConfirmation();
            }
        });
        console.log('[DEBUG] initializeEventListeners: 模态框背景点击事件已绑定');
    } else {
        console.warn('[DEBUG] initializeEventListeners: 未找到模态框元素');
    }

    console.log('[DEBUG] initializeEventListeners: 事件监听器初始化完成');
}

// 检查Chrome扩展权限和API可用性
async function checkPermissions() {
    console.log('[DEBUG] checkPermissions: 检查Chrome扩展权限...');

    if (!chrome) {
        console.error('[DEBUG] checkPermissions: Chrome API 不可用');
        return false;
    }

    if (!chrome.tabs) {
        console.error('[DEBUG] checkPermissions: Chrome tabs API 不可用');
        return false;
    }

    try {
        // 尝试查询标签页以测试权限
        const tabs = await chrome.tabs.query({ currentWindow: true });
        console.log('[DEBUG] checkPermissions: 权限检查成功，当前窗口标签页数量:', tabs.length);
        return true;
    } catch (error) {
        console.error('[DEBUG] checkPermissions: 权限检查失败:', error);
        return false;
    }
}

// 工作区窗口单例管理
class WorkspaceWindowManager {
    constructor() {
        this.workspaceWindowId = null;
        this.isCreatingWindow = false;
    }

    // 检查窗口是否存在
    async isWindowExists(windowId) {
        if (!windowId) return false;

        try {
            await chrome.windows.get(windowId);
            return true;
        } catch (error) {
            console.log('[DEBUG] WorkspaceWindowManager: 窗口不存在:', windowId);
            return false;
        }
    }

    // 获取或创建工作区窗口
    async getOrCreateWorkspaceWindow() {
        console.log('[DEBUG] WorkspaceWindowManager: 获取或创建工作区窗口');

        // 防止并发创建多个窗口
        if (this.isCreatingWindow) {
            console.log('[DEBUG] WorkspaceWindowManager: 正在创建窗口，等待...');
            // 等待当前创建操作完成
            while (this.isCreatingWindow) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            return this.workspaceWindowId;
        }

        // 检查现有窗口是否仍然存在
        if (this.workspaceWindowId && await this.isWindowExists(this.workspaceWindowId)) {
            console.log('[DEBUG] WorkspaceWindowManager: 使用现有工作区窗口:', this.workspaceWindowId);
            return this.workspaceWindowId;
        }

        // 创建新的工作区窗口
        this.isCreatingWindow = true;
        try {
            console.log('[DEBUG] WorkspaceWindowManager: 创建新的工作区窗口');
            const window = await chrome.windows.create({
                url: chrome.runtime.getURL('workspace-placeholder.html'),
                type: 'normal',
                state: 'maximized'
            });

            this.workspaceWindowId = window.id;
            console.log('[DEBUG] WorkspaceWindowManager: 新工作区窗口已创建:', this.workspaceWindowId);

            // 监听窗口关闭事件
            chrome.windows.onRemoved.addListener((windowId) => {
                if (windowId === this.workspaceWindowId) {
                    console.log('[DEBUG] WorkspaceWindowManager: 工作区窗口已关闭，重置ID');
                    this.workspaceWindowId = null;
                }
            });

            return this.workspaceWindowId;
        } catch (error) {
            console.error('[DEBUG] WorkspaceWindowManager: 创建工作区窗口失败:', error);
            this.workspaceWindowId = null;
            throw error;
        } finally {
            this.isCreatingWindow = false;
        }
    }

    // 将标签页移动到工作区窗口
    async moveTabsToWorkspace(tabIds) {
        console.log('[DEBUG] WorkspaceWindowManager: 移动标签页到工作区:', tabIds);

        try {
            const windowId = await this.getOrCreateWorkspaceWindow();
            if (!windowId) {
                throw new Error('无法获取工作区窗口');
            }

            // 移动标签页到工作区窗口
            await chrome.tabs.move(tabIds, { windowId, index: -1 });
            console.log('[DEBUG] WorkspaceWindowManager: 标签页移动完成');

            // 激活工作区窗口
            await chrome.windows.update(windowId, { focused: true });

            return windowId;
        } catch (error) {
            console.error('[DEBUG] WorkspaceWindowManager: 移动标签页失败:', error);
            throw error;
        }
    }
}

// 创建全局工作区窗口管理器实例
const workspaceWindowManager = new WorkspaceWindowManager();

// 初始化所有功能
async function initializeAll() {
    console.log('[DEBUG] initializeAll: 开始初始化页面...');
    console.log('[DEBUG] initializeAll: 当前URL:', window.location.href);
    console.log('[DEBUG] initializeAll: Document readyState:', document.readyState);

    // 检查权限
    const hasPermissions = await checkPermissions();
    if (!hasPermissions) {
        console.error('[DEBUG] initializeAll: 缺少必要权限，无法正常工作');
        // 显示错误信息给用户
        const tabsSection = document.getElementById('tabsSection');
        if (tabsSection) {
            tabsSection.innerHTML = `
                <div class="error-message">
                    <h3>⚠️ 权限错误</h3>
                    <p>扩展缺少必要的标签页访问权限。请检查扩展设置。</p>
                </div>
            `;
            tabsSection.style.display = 'block';
        }
        return;
    }

    initializeEventListeners();
    await updateTabCount();
    await loadTabsList();

    // 设置定期更新
    setInterval(async () => {
        await updateTabCount();
        await loadTabsList();
    }, 3000); // 每3秒更新一次

    console.log('[DEBUG] initializeAll: 页面初始化完成');
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAll);
} else {
    // DOM已经加载完成，立即初始化
    initializeAll();
}
